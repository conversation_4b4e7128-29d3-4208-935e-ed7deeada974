{"expo": {"owner": "sfxmoneyapp", "name": "Sfx", "slug": "Sfx", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "scheme": "com.sfx", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": false, "googleServicesFile": "./GoogleService-Info.plist", "bundleIdentifier": "com.sfx", "buildNumber": "45", "usesAppleSignIn": true, "infoPlist": {"NSFaceIDUsageDescription": "We use Face ID to securely authenticate you into the app.", "NSCameraUsageDescription": "Sfx requires access to your camera to scan QR codes for initiating secure money transfers and to capture your photo for identity verification during the KYC (Know Your Customer) process. For example, your captured image helps verify your identity as part of regulatory compliance.", "NSPhotoLibraryUsageDescription": "Sfx needs permission to access your photo library to set custom avatar"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-services.json", "package": "com.henry.sfxmoneyapp", "intentFilters": [{"action": "VIEW", "data": [{"scheme": "com.sfx"}], "category": ["BROWSABLE", "DEFAULT"]}], "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT", "android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT"]}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"ENCRYPTION_KEY": "bb6b6ec4e8963bfd5471dfc93bd1e2bba77e3f0370d224c90694e6dfc7ea6aa1", "ENCRYPTION_IV": "a9044e9414711bcdf2e29354db7c7b44", "KEY": "2ac96a576fe72e4ae1ba2e60a24977c5efa34deabed56ed5207e56669fc3ebd1", "STAGING_BASE_URL": "https://dev-api.sfxchange.co/", "PROD_BASE_URL": "https://prod-api.sfxchange.co/", "eas": {"projectId": "f9b5b171-3c20-47a8-a076-204f035c9262"}}, "plugins": [["expo-camera", {"cameraPermission": "Allow Sfx to access your camera to scan QR codes for quick actions like internal or external transfers.", "recordAudioAndroid": true}], ["expo-media-library", {"photosPermission": "Allow Sfx to access your photo library so you can view and manage saved receipts.", "savePhotosPermission": "Allow Sfx to save receipt images to your photo library for your records.", "isAccessMediaLocationEnabled": true}], "expo-localization", "@react-native-google-signin/google-signin", ["expo-build-properties", {"android": {"minSdkVersion": 24}, "ios": {"deploymentTarget": "15.1", "useFrameworks": "static", "podfileProperties": {"use_modular_headers!": true}}}], ["expo-local-authentication", {"faceIDPermission": "Allow Sfx to use Face ID for biometric authentication."}], ["expo-secure-store", {"configureAndroidBackup": true}], ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/splash-icon.png", "resizeMode": "contain"}], "react-native-compressor", "expo-font", "expo-apple-authentication", "expo-secure-store"], "runtimeVersion": "1.0.0", "updates": {"enabled": true, "url": "https://u.expo.dev/f9b5b171-3c20-47a8-a076-204f035c9262", "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}}}