import AsyncStorage from "@react-native-async-storage/async-storage";

interface RecentlyUsedBeneficiary {
  id: string;
  account: string;
  name: string;
  type: string;
  providerName: string;
  country: string;
  sfxBeneficiaryUser?: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    picture?: string;
    verified: string;
  };
  lastUsed: number; // timestamp
}

const CACHE_KEY = 'recentlyUsedSfxBeneficiaries';
const MAX_RECENT_ITEMS = 10; // Maximum number of recent beneficiaries to store

/**
 * Utility functions for managing recently used SFx beneficiaries cache
 * This helps show the most recently used beneficiaries at the top
 */

export const getRecentlyUsedBeneficiaries = async (): Promise<RecentlyUsedBeneficiary[]> => {
  try {
    const cachedData = await AsyncStorage.getItem(CACHE_KEY);
    if (cachedData) {
      const recentBeneficiaries: RecentlyUsedBeneficiary[] = JSON.parse(cachedData);
      // Sort by lastUsed timestamp (most recent first)
      return recentBeneficiaries.sort((a, b) => b.lastUsed - a.lastUsed);
    }
    return [];
  } catch (error) {
    console.error("Error loading recently used beneficiaries cache:", error);
    return [];
  }
};

export const addRecentlyUsedBeneficiary = async (beneficiary: Omit<RecentlyUsedBeneficiary, 'lastUsed'>): Promise<void> => {
  try {
    const currentBeneficiaries = await getRecentlyUsedBeneficiaries();
    const currentTime = new Date().getTime();
    
    // Check if beneficiary already exists (by account/username)
    const existingIndex = currentBeneficiaries.findIndex(
      item => item.account === beneficiary.account && item.type === 'sfx-money-app'
    );
    
    let updatedBeneficiaries: RecentlyUsedBeneficiary[];
    
    if (existingIndex !== -1) {
      // Update existing beneficiary's timestamp and move to top
      updatedBeneficiaries = [...currentBeneficiaries];
      updatedBeneficiaries[existingIndex] = {
        ...beneficiary,
        lastUsed: currentTime
      };
      // Move to front
      const updatedItem = updatedBeneficiaries.splice(existingIndex, 1)[0];
      updatedBeneficiaries.unshift(updatedItem);
    } else {
      // Add new beneficiary at the beginning
      const newBeneficiary: RecentlyUsedBeneficiary = {
        ...beneficiary,
        lastUsed: currentTime
      };
      updatedBeneficiaries = [newBeneficiary, ...currentBeneficiaries];
    }
    
    // Keep only the most recent items
    updatedBeneficiaries = updatedBeneficiaries.slice(0, MAX_RECENT_ITEMS);
    
    await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(updatedBeneficiaries));
    console.log(`Added/Updated recently used beneficiary: ${beneficiary.account}`);
  } catch (error) {
    console.error("Error saving recently used beneficiary:", error);
  }
};

export const removeRecentlyUsedBeneficiary = async (account: string): Promise<void> => {
  try {
    const currentBeneficiaries = await getRecentlyUsedBeneficiaries();
    const updatedBeneficiaries = currentBeneficiaries.filter(
      item => item.account !== account
    );
    
    await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(updatedBeneficiaries));
    console.log(`Removed recently used beneficiary: ${account}`);
  } catch (error) {
    console.error("Error removing recently used beneficiary:", error);
  }
};

export const clearRecentlyUsedBeneficiaries = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(CACHE_KEY);
    console.log("Cleared all recently used beneficiaries");
  } catch (error) {
    console.error("Error clearing recently used beneficiaries:", error);
  }
};

/**
 * Merge recently used beneficiaries with regular beneficiaries
 * Recently used ones appear at the top, avoiding duplicates
 */
export const mergeWithRecentlyUsed = async (regularBeneficiaries: any[]): Promise<any[]> => {
  try {
    const recentlyUsed = await getRecentlyUsedBeneficiaries();
    
    // Filter out recently used items from regular beneficiaries to avoid duplicates
    const filteredRegular = regularBeneficiaries.filter(regular => 
      !recentlyUsed.some(recent => 
        recent.account === regular.account && recent.type === regular.type
      )
    );
    
    // Convert recently used to the same format as regular beneficiaries
    const formattedRecentlyUsed = recentlyUsed.map(recent => ({
      ...recent,
      isRecentlyUsed: true // Add flag to identify recently used items
    }));
    
    // Combine: recently used first, then regular beneficiaries
    return [...formattedRecentlyUsed, ...filteredRegular];
  } catch (error) {
    console.error("Error merging recently used beneficiaries:", error);
    return regularBeneficiaries;
  }
};

/**
 * Check if a beneficiary is in the recently used list
 */
export const isRecentlyUsed = async (account: string): Promise<boolean> => {
  try {
    const recentlyUsed = await getRecentlyUsedBeneficiaries();
    return recentlyUsed.some(item => item.account === account);
  } catch (error) {
    console.error("Error checking if beneficiary is recently used:", error);
    return false;
  }
};
