import React, { useContext } from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { StyleSheet, Dimensions, Platform } from "react-native";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
// import SettingsScreen from "../screens/SettingsScreen";
import P from "../components/P";
// import ReferralScreen from "../screens/ReferralScreen";
import HomeScreen from "../screens/HomeScreen";
import SettingScreen from "../screens/SettingScreen";
import WalletScreen from "../screens/wallet/WalletScreen";
import HistoryScreen from "../screens/History/History";
import { colors } from "../config/colors";
import { useRoute } from "@react-navigation/native";
import { CredentailsContext } from "../RequestHandlers/CredentailsContext";
import AccountVerificationPromt1 from "../screens/Card/AccountVerificationPromt1";
import NewCardScreen from "../screens/NewCard/NewCardScreen";

const baseHeight = 802;
const { width, height } = Dimensions.get("window");
const isTablet = Dimensions.get("window").width >= 700;
const BottomTabNavigator = () => {
  const route = useRoute();
  const { storedCredentails } = useContext(CredentailsContext)
  const Tab = createBottomTabNavigator();
  return (
    <Tab.Navigator
      screenOptions={{
        // tabBarActiveTintColor: "#fff",
        tabBarHideOnKeyboard: true,
        // tabBarInactiveTintColor: "gray",
        tabBarStyle: [
          {
            position: "absolute",
            height: Platform.OS === "ios" ? 70 : 70,
            paddingBottom: Platform.OS === "ios" ? (2.5 * height) / 100 : 16,
            paddingTop: 16,
            backgroundColor: "#fff",
            justifyContent: "space-around",
            borderColor: "#fff",
            paddingLeft: (3 * width) / 100,
            paddingRight: (3 * width) / 100,
            borderTopWidth: 0,
            elevation: 0,
          },
          Platform.OS === "ios"
            ? {
                backgroundColor: "#fff",
                shadowColor: colors.gray,
                shadowOffset: { width: 0, height: -1 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
              }
            : { 
                elevation: 8,
                backgroundColor: "#fff",
              },
        ],
        tabBarLabelStyle: {
          fontFamily: fonts.poppinsMedium,
          fontSize: 11,
          // marginTop: 4,
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        initialParams={route.params}
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.homeActive} />
            ) : (
              <SvgXml xml={svg.home} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused
                  ? "rgba(139, 82, 255, 1)"
                  : "#5D5B5B",
                fontFamily: fonts.poppinsMedium,
                fontSize: 12,
                marginLeft: isTablet ? 15 : 0,
                textAlign: "center",
                marginTop: 4,
              }}
            >
              Home
            </P>
          ),
        }}
      />
      <Tab.Screen
        name="Wallet"
        component={WalletScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.walletActive} />
            ) : (
              <SvgXml xml={svg.wallet} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused
                  ? "rgba(139, 82, 255, 1)"
                  : "#5D5B5B",
                fontFamily: fonts.poppinsMedium,
                fontSize: 12,
                marginLeft: isTablet ? 15 : 0,
                marginTop: 4,
              }}
            >
              Wallet
            </P>
          ),
        }}
      />
      <Tab.Screen
        name="Card"
        component={NewCardScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.cardActive} />
            ) : (
              <SvgXml xml={svg.cardOutline} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused
                  ? "rgba(139, 82, 255, 1)"
                  : "#5D5B5B",
                fontFamily: fonts.poppinsMedium,
                fontSize: 12,
                marginLeft: isTablet ? 15 : 0,
                marginTop: 4,
              }}
            >
              Card
            </P>
          ),
        }}
      />
      <Tab.Screen
        name="History"
        component={HistoryScreen}
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.sCoinActive} />
            ) : (
              <SvgXml xml={svg.sCoin} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused ? colors.primary : "#5D5B5B",
                fontFamily: fonts.poppinsMedium,
                fontSize: 12,
                marginLeft: isTablet ? 15 : 0,
                marginTop: 4,
              }}
            >
              History
            </P>
          ),
        }}
      />
      <Tab.Screen
        name="Settings"
        component={storedCredentails && storedCredentails.user !== null && storedCredentails.user.username ? SettingScreen : AccountVerificationPromt1}
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.settingsActive} />
            ) : (
              <SvgXml xml={svg.settings} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused
                  ? "rgba(139, 82, 255, 1)"
                  : "#5D5B5B",
                fontFamily: fonts.poppinsMedium,
                fontSize: 12,
                marginLeft: isTablet ? 15 : 0,
                marginTop: 4,
              }}
            >
              Settings
            </P>
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({});

export default BottomTabNavigator;
