import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  RefreshControl,
  Platform,
  ActivityIndicator,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import UserHeader from "../components/UserHeader";
import Div from "../components/Div";
import MicroBtn from "../components/MicroBtn";
import { svg } from "../config/Svg";
import { SvgXml } from "react-native-svg";
import P from "../components/P";
import Modal from "react-native-modal";
import { fonts } from "../config/Fonts";
import { colors } from "../config/colors";
import BottomSheet from "../components/BottomSheet";
import SelectPaymentType from "../components/SelectPaymentType";
import CountrySelect from "../components/CountrySelect";
import AccountSelect from "../components/AccountSelect";
import TransactionItem from "../components/TransactionItem";
import NoteComponent2 from "../components/NoteComponent2";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Button from "../components/Button";
import Rates from "./Rate/Rate";
import Discover from "./Discover";
import BarCodeScanner from "../components/BarCodeScanner";
import PCard from "../components/PCard";
import i18n from "../../i18n";

import { GetUserDetails } from "../RequestHandlers/User";
import Loader from "../components/ActivityIndicator";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import { HomeSkeleton } from "../Skeletons/Skeletons";
import { useFocusEffect } from "@react-navigation/native";
import { GetTransation, GetUserWallet } from "../RequestHandlers/Wallet";
import { countries } from "../components/counties";
import { formatDate } from "../components/FormatDate";
import { TabView, SceneMap } from "react-native-tab-view";
import { TabBar } from "react-native-tab-view";
import PersonalizedR from "../components/PersonalizedR";
import { GetNotifications } from "../RequestHandlers/notification";
import * as Clipboard from "expo-clipboard";
import CardIssue from "../components/CardIssue";
import {
  getTransactionIcon,
  getTransactionLabel,
  TransactionClick,
} from "../Utils/TransactionClick";
import EnableNotification from "../components/ErrorSate/Enablenotification";
import ForYouComponent from "./Guide/ForYouComponent";
import { useToast } from "../context/ToastContext";
import MainRates from "./Rate/NewRate";
import CurrencySelect from "../components/CurrencySelect";
import HomeBalance from "./HomeBalance";
import WelcomeModal from "../components/WelcomeModal";
import { useWelcomeModal } from "../context/WelcomeModalContext";
import {
  formatNumberWithCommas,
  formatToTwoDecimals,
} from "../Utils/numberFormat";
import { GetReferalCountdown } from "../RequestHandlers/Referral";
import { withApiErrorToast } from "../Utils/withApiErrorToast";
import NonVerifiedAppHeader from "../components/NonVerifiedAppHeader";
// import BillPaymentScreen from "./BillPaymentScreen";

const defaultItems = [
  { text: "Water", svg: svg.droplet, route: "WaterIndex" },
  { text: "Electricity", svg: svg.electricity, route: "ElecricityIndex" },
  { text: "Airtime", svg: svg.phone, route: "AirtimeIndex" },
];
const allActiveBillItems = [
  { text: "Water", svg: svg.droplet, route: "WaterIndex" },
  { text: "Electricity", svg: svg.electricity, route: "ElecricityIndex" },
  { text: "Airtime", svg: svg.phone, route: "AirtimeIndex" },
  { text: "Internet", svg: svg.internet, route: "InternetIndex" },
];
const { width, height } = Dimensions.get("window");
const initialLayout = { width: Dimensions.get("window").width };
const baseHeight = 802;
export default function HomeScreen({ navigation }) {
  const [isModalVisible, setModalVisible] = useState(false);
  const [amount, setAmount] = useState(0);
  const [trAmount, setTrAmount] = useState(0);
  const [isKycDone, setIsKycDone] = useState("false");
  const [accVerification, setAccVerification] = useState("");
  const [activePaymentType, setActivePaymentType] = useState(null);
  const [activeAccIndex, setActiveAccIndex] = useState(0);
  const activeAccIndexRef = useRef<number | null>(null);
  const activePaymentTypeRef = useRef<number | null>(null);
  const [cardRegisterd, setCardRegisterd] = useState(false);
  const [billItems, setBillItems] = useState([...defaultItems]);
  const [selectedItems, setSelectedItems] = useState([...defaultItems]);
  const [hideBal, setHideBal] = useState(false);
  const [isBottomSheetVisible, setBottomSheetVisible] = useState(false);
  const [isSaveDisabled, setIsSaveDisabled] = useState(true);
  const [showQrCode, setSHowQrCode] = useState(false);
  const [isNewNoti, setIsNewNoti] = useState(false);
  const [loader, setLoader] = useState(false);
  const [uDetails, setUDetails] = useState<any>([]);
  const [wallets, setWallets] = useState([]);
  const [curFlag, setCurFlag] = useState(require("../assets/turkey.png"));
  const [cur, setCur] = useState("Turkish lira");
  const [curSymbol, setCurSymbol] = useState("₺");
  const [curCode, setCurCode] = useState("TRY");
  const [localAmount, setLocalAmount] = useState(0);
  const [transactions, setTransations] = useState([]);
  const [username, setUserName] = useState("");
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [iscardInreview, setIsCardInreview] = useState(false);
  const [isPushNotificationEnabled, setIsPushNotificationEnabled] =
    useState(true);
  const { handleToast } = useToast();
  const [isSwipEnabled, setIsSwipEnabled] = useState(true);
  const { showWelcomeModal, setNavigation } = useWelcomeModal();
  const [countdownSeconds, setCountdownSeconds] = useState(0);
  const [errormessage, setErrorMessage] = useState("Message");
  const [profilePic, setProfilePic] = useState("");
  const [isReferalProgram, setIsReferalProgram] = useState(true);
  const accDetails = `${uDetails.username}`;
  const copyAccNum = async () => {
    const copiedText = await Clipboard.setStringAsync(accDetails);
    if (copiedText === true) {
      setIsRefCopied(true);
      setTimeout(() => {
        setIsRefCopied(false);
      }, 4000);
    }
  };
  function capitalizeFirstLetter(word) {
    if (!word) return "";
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }
  function ensureHttps(url) {
    if (url?.startsWith("http://")) {
      return url?.replace("http://", "https://");
    }
    return url;
  }
  const getSymbol = (currencyCode) => {
    if (currencyCode === "TRY") {
      return "₺";
    }
    const curSymbol = countries.find(
      (item) => item.currencyCode === currencyCode
    );
    return curSymbol ? curSymbol.symbol : "$";
  };
  useEffect(() => {
    setNavigation(navigation);
  }, [navigation]);
  useEffect(() => {
    activeAccIndexRef.current = activeAccIndex;
  }, [activeAccIndex]);
  useEffect(() => {
    activePaymentTypeRef.current = activePaymentType;
  }, [activePaymentType]);

  const tabLinks = ["Accounts", "Rate", "Discover"];
  const nonKycFrameData = [
    {
      text1: "Personalized recommendation",
      text2: "Enjoy exclusive offers by completing your account verification",
      image: require("../assets/id.png"),
    },
  ];
  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  useEffect(() => {
    const loadSelectedItems = async () => {
      try {
        const savedItems = await AsyncStorage.getItem("billItems");
        if (savedItems) {
          setSelectedItems(JSON.parse(savedItems));
        }
      } catch (error) {
        console.error("Failed to load bill items:", error);
      }
    };

    if (isBottomSheetVisible) {
      loadSelectedItems();
    }
  }, [isBottomSheetVisible]);
  useEffect(() => {
    const loadSelectedItems = async () => {
      try {
        const savedItems = await AsyncStorage.getItem("billItems");
        if (savedItems) {
          setSelectedItems(JSON.parse(savedItems));
        }
      } catch (error) {
        console.error("Failed to load bill items:", error);
      }
    };

    loadSelectedItems();
  }, []);

  const handleSave = async () => {
    if (selectedItems?.length === 3) {
      const updatedItems = [...selectedItems];
      setBillItems(updatedItems);
      try {
        await AsyncStorage.setItem("billItems", JSON.stringify(updatedItems));
      } catch (error) {
        console.error("Failed to save bill items:", error);
      }
    }
    setBottomSheetVisible(false);
  };

  const handleItemSelection = (item) => {
    const itemIndex = selectedItems.findIndex(
      (selectedItem) => selectedItem.text === item.text
    );
    if (itemIndex !== -1) {
      const updatedItems = [...selectedItems];
      updatedItems[itemIndex] = { text: "", svg: null, route: "" };
      setSelectedItems(updatedItems);
    } else {
      const emptyIndex = selectedItems.findIndex(
        (selectedItem) => !selectedItem.text
      );
      if (emptyIndex !== -1) {
        const updatedItems = [...selectedItems];
        updatedItems[emptyIndex] = item;
        setSelectedItems(updatedItems);
      } else if (selectedItems?.length < 3) {
        setSelectedItems([...selectedItems, item]);
      }
    }
  };

  useEffect(() => {
    const hasSelectedItems = selectedItems.some((item) => item.text !== "");
    setIsSaveDisabled(!hasSelectedItems);
  }, [selectedItems]);

  const getAllNoti = async () => {
    try {
      const res = await GetNotifications(1, 20, "all");
      let hasUnreadPayment = false;
      res.items.forEach((notification) => {
        if (notification.status === "unread") {
          hasUnreadPayment = true;
        }
      });
      setIsNewNoti(hasUnreadPayment);
    } catch (error) {}
  };
  // integration
  const getUserDetails = async () => {
    try {
      const userDetails = await withApiErrorToast(
        GetUserDetails(),
        handleToast
      );
      if (!userDetails) return;
      if (userDetails.error) {
        handleToast(userDetails.message, "error");
      } else {
        if (userDetails.email) {
          AsyncStorage.removeItem("GToken").then((res) => {});
          setProfilePic(userDetails?.picture);
          setLoader(false);
          setUDetails(userDetails);
          if (userDetails.verified) {
            setIsKycDone(userDetails.verified);
          }
          setIsCardInreview(userDetails.bridgeCardflagForManualReview);
          setAccVerification(userDetails.verified);
          setUserName(userDetails.username);
          setCardRegisterd(userDetails?.cards?.length === 0 ? false : true);
          setErrorMessage(userDetails?.tier?.message);
        }
      }
      //   if (!userDetails.homeCountry) {
      //     navigation.navigate("AccountVerificationPromt");
      //   } else if (!userDetails.username) {
      //     navigation.navigate("AccountVerificationPromt");
      //   } else if (!userDetails?.hasPin) {
      //     navigation.navigate("AccountVerificationPromt");
      //   } else {
    } catch (error) {
      handleToast("Network error", "error");
    } finally {
      setLoader(false);
    }
  };

  const pushNotificationStatus = async () => {
    try {
      const res = await withApiErrorToast(GetUserDetails(), handleToast);
      if (!res) return;
      if (res.pushnotification === false) {
        setIsPushNotificationEnabled(false);
      } else {
        setIsPushNotificationEnabled(true);
      }
    } finally {
    }
  };
  const getTransaction = async () => {
    try {
      const transactions = await withApiErrorToast(
        GetTransation(),
        handleToast
      );
      const sortedTransactions = transactions.items.sort((a, b) => {
        return (
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        ); // Convert to timestamps
      });
      setTransations(sortedTransactions);
    } catch (error) {}
  };
  useEffect(() => {
    setLoader(true);
    pushNotificationStatus();
  }, []);

  const removeJustLoginFlag = async () => {
    await AsyncStorage.removeItem("justLoggedIn");
  };
  const getReferralCountdown = async () => {
    try {
      const res = await GetReferalCountdown();
      if (res.totalSeconds) {
        setCountdownSeconds(res.totalSeconds);
      }
    } catch (error) {}
  };
  // Initialize welcome modal based on newUser parameter
  useEffect(() => {
    getReferralCountdown();
    const initializeWelcomeModal = async () => {
      try {
        const hasLoggedInBefore = await AsyncStorage.getItem("newUser");

        if (hasLoggedInBefore === "true") {
          showWelcomeModal();
        }
        setTimeout(() => {
          removeJustLoginFlag();
        }, 1000);
      } catch (error) {
        console.error("Error in welcome modal initialization:", error);
      }
    };

    initializeWelcomeModal();
  }, []);
  useFocusEffect(
    useCallback(() => {
      getUserDetails();
      getTransaction();
      getAllNoti();
    }, [])
  );
  const onRefresh = () => {
    setRefreshing(true);
    getUserDetails();
    getTransaction();
    getAllNoti();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  // const closeWelcomeModal = async () => {
  //   await AsyncStorage.setItem("hasLoggedInBefore", "true");
  //   await AsyncStorage.removeItem("newUser");
  //   showWelcomeModal();
  // };

  const handleScan = (index: any) => {
    if (index?.length <= 20) {
      navigation.navigate("AccountDetailsEntry", { data: index });
    } else if (index?.length === 42) {
      navigation.navigate("P2pScreen", { data: index });
    } else if (index?.length === 34) {
      navigation.navigate("P2pScreen", { data: index });
    } else {
      handleToast("invalid wallet address", "error");
    }
  };
  const [index, setIndex] = React.useState(0);
  const [routes] = React.useState([
    { key: "first", title: "Accounts" },
    { key: "second", title: "Rate" },
    // { key: "third", title: "Discover" },
  ]);
  const renderScene = ({ route }) => {
    switch (route.key) {
      case "first":
        return (
          <>
            <ScrollView
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
              }
              contentContainerStyle={{ paddingBottom: 100 }}
              showsVerticalScrollIndicator={false}
            >
              <HomeBalance
                onScrollBeginDrag={() => setIsSwipEnabled(false)}
                onScrollEndDrag={() => setIsSwipEnabled(true)}
                onTouchStart={() => {
                  setIsSwipEnabled(false);
                }}
                onTouchEnd={() => {
                  setIsSwipEnabled(true);
                }}
                refreshing={refreshing}
                onRefresh={onRefresh}
              />

              <View style={[styles.card, { height: 79 }]}>
                <View
                  style={[
                    styles.actions,
                    // { paddingLeft: 20, paddingRight: 20 },
                  ]}
                >
                  <ActionButton
                    text="Add"
                    svg={svg.add}
                    onPress={() =>
                      isKycDone === "true"
                        ? navigation.navigate("AddMoneyScreen")
                        : isKycDone === "pending"
                        ? navigation.navigate("AccountVerificationPending")
                        : navigation.navigate("AccountVerificationPromt")
                    }
                  />
                  <ActionButton
                    text="Send"
                    svg={svg.send}
                    onPress={() =>
                      isKycDone === "true"
                        ? navigation.navigate("SendMoneyScreen")
                        : isKycDone === "pending"
                        ? navigation.navigate("AccountVerificationPending")
                        : navigation.navigate("AccountVerificationPromt")
                    }
                  />
                  <ActionButton
                    text="OTC desk"
                    svg={svg.deck}
                    onPress={() => {
                      // navigation.navigate("")
                      isKycDone === "true"
                        ? navigation.navigate("OTCDeckSelectScreen")
                        : isKycDone === "pending"
                        ? navigation.navigate("AccountVerificationPending")
                        : navigation.navigate("AccountVerificationPromt");
                    }}
                  />
                  {/* <ActionButton
                    text="Card"
                    svg={svg.card}
                    onPress={() => {
                      // navigation.navigate("AccountVerification4")
                      if (cardRegisterd === true) {
                        navigation.navigate("CardScreen");
                      } else {
                        navigation.navigate("CardGetStartedScreen");
                      }
                    }}
                  /> */}
                  <ActionButton
                    text="Refer"
                    svg={svg.starFace}
                    onPress={() => {
                      if (isReferalProgram) {
                        navigation.navigate("ReferralContestScreen", {
                          countdownSeconds,
                        });
                      } else {
                        navigation.navigate("ReferralProgramScreen");
                        // navigation.navigate("ReferralScreen")
                      }
                    }}
                    // onPress={() => {
                    //   navigation.navigate("SumsubKYCScreen");
                    // }}
                  />
                </View>
              </View>
              {iscardInreview ? (
                <TouchableOpacity
                  style={{
                    width: "90%",
                    alignSelf: "center",
                    marginBottom: 24,
                  }}
                  onPress={() => {
                    if (uDetails?.cardHolderNote?.includes("try again")) {
                      navigation.navigate("CardApplicationScreen");
                    }
                  }}
                >
                  <CardIssue text={uDetails?.cardHolderNote} />
                </TouchableOpacity>
              ) : isKycDone === "true" ? (
                <>
                  <PersonalizedR
                    navigation={navigation}
                    isCardRegisterd={cardRegisterd}
                    countdownSeconds={countdownSeconds}
                  />
                </>
              ) : (
                <></>
                // <TouchableOpacity
                //   style={{
                //     marginBottom: 24,
                //     width: "90%",
                //     alignSelf: "center",
                //   }}
                //   onPress={() => {
                //     if (uDetails?.verified === "pending") {
                //       navigation.navigate("AccountVerificationPending");
                //     } else if (uDetails?.verified === "false") {
                //       navigation.navigate("AccountVerificationPromt");
                //     } else if (uDetails.verified === "failed") {
                //       navigation.navigate("AccountVerification4");
                //     } else {
                //     }
                //   }}
                // >
                // <PCard
                //   accStatus={accVerification}
                //   navigation={navigation}
                //   errorMessage={errormessage}
                // />
                // </TouchableOpacity>
              )}
              <View
                style={[
                  styles.card,
                  {
                    padding: 0,
                    minHeight: 246,
                    // marginBottom: 100,
                    // marginTop: 16,
                  },
                ]}
              >
                <View
                  style={{
                    width: "100%",
                    borderBottomWidth: 1,
                    borderColor: "rgba(240, 239, 239, 1)",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    // height: 45,
                    alignItems: "center",
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    // marginBottom: "5%",
                  }}
                >
                  <P
                    style={{
                      color: colors.dGray,
                      fontSize: 12,
                    }}
                  >
                    Recent transaction
                  </P>
                  {transactions?.length > 4 && (
                    <TouchableOpacity
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      onPress={() => {
                        navigation.navigate("History");
                      }}
                    >
                      <P
                        style={{
                          fontSize: 14,
                          marginRight: 5,
                          color: colors.primary,
                          textDecorationLine: "underline",
                        }}
                      >
                        See all
                      </P>
                      {/* <SvgXml xml={svg.arroveRight} /> */}
                    </TouchableOpacity>
                  )}
                </View>

                <View style={[styles.transactions]}>
                  {transactions?.length === 0 ? (
                    <View style={{ width: "100%", alignItems: "center" }}>
                      <SvgXml
                        xml={svg.noTransaction}
                        style={{
                          marginTop: 52,
                          marginBottom: 16,
                        }}
                      />
                      <P
                        style={{
                          fontFamily: fonts.poppinsMedium,
                          marginBottom: 4,
                          fontSize: 12,
                        }}
                      >
                        No transaction!
                      </P>
                      <P
                        style={{
                          color: "#A5A1A1",
                          fontFamily: fonts.poppinsRegular,
                          fontSize: 12,
                        }}
                      >
                        You have no transaction yet
                      </P>
                    </View>
                  ) : (
                    transactions?.slice(0, 4).map((item, index) => (
                      <TransactionItem
                        key={index}
                        onPress={() => {
                          TransactionClick(item, navigation);
                        }}
                        titleContStyle={{ width: (50 * width) / 100 }}
                        bal={
                          item?.status === "completed"
                            ? `$${formatToTwoDecimals(
                                Number(item?.balanceAfter)
                              )}`
                            : `$${formatToTwoDecimals(0)}`
                        }
                        icon={getTransactionIcon(item, svg)}
                        itemStyle={{
                          borderBottomWidth:
                            index === transactions?.slice(0, 4)?.length - 1
                              ? 0
                              : 1,
                          borderColor: "#F0EFEF",
                          alignItems: "center",
                          paddingLeft: 16,
                          paddingRight: 16,
                          // paddingBottom: 20,
                          // paddingTop: index === 0 ? 20 : 0,
                        }}
                        fee={`${item.fee > 0 ? "-" : ""}$${
                          item?.type === "DEPOSIT"
                            ? formatNumberWithCommas(
                                item.fee / item.exchangeRate
                              )
                            : formatNumberWithCommas(item.fee)
                        }`}
                        currency={
                          item.sfxToSfxCurrency ? item.sfxToSfxCurrency : "USD"
                        }
                        // ${
                        //   item.type === "DEPOSIT" ||
                        //   item?.internalTransferSender
                        //     ? "+"
                        //     : "-"
                        // }
                        amount={`${
                          item.sfxToSfxCurrency &&
                          item.sfxToSfxCurrency !== "USD"
                            ? getSymbol(item.sfxToSfxCurrency)
                            : "$"
                        }${
                          item.sfxToSfxCurrency &&
                          item.sfxToSfxCurrency !== "USD"
                            ? formatToTwoDecimals(item.localAmount)
                            : item?.amount
                            ? formatToTwoDecimals(item.amount)
                            : "..."
                        }`}
                        title={`${getTransactionLabel(item)}`}
                        date={formatDate(item?.updatedAt)}
                        status={
                          item?.status === "completed"
                            ? "Successful"
                            : item?.status === "processing"
                            ? "Pending"
                            : item?.status?.includes("awaiting-confirmation")
                            ? "Pending"
                            : capitalizeFirstLetter(item?.status)
                        }
                        statusStyle={{
                          // textAlign: "right",
                          color: item?.status
                            ?.toLowerCase()
                            .includes("completed")
                            ? colors.green
                            : item?.status?.toLowerCase().includes("pending") ||
                              item?.status
                                ?.toLowerCase()
                                .includes("processing") ||
                              item?.status
                                ?.toLowerCase()
                                .includes("awaiting-confirmation")
                            ? colors.yellow
                            : colors.red,
                        }}
                      />
                    ))
                  )}
                </View>
              </View>
              {/* <ForYouComponent navigation={navigation} /> */}

              {/* transaction end */}
            </ScrollView>
          </>
        );
      case "second":
        return <MainRates navigation={navigation} />;
      // case "third":
      //   return <Discover />;
      default:
        return null;
    }
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <>
      <LinearGradient
        colors={["rgba(139, 82, 255, 1)", "#F7F4FF"]}
        locations={[0.2, 0.3]}
        style={styles.gradient}
      >
        <Div>
          {loader ? (
            // HomeSkeleton()
            <></>
          ) : (
            <>
              {isKycDone !== "true" ? (
                <NonVerifiedAppHeader
                  Imageurl={profilePic}
                  isKycDone={isKycDone}
                  navigation={navigation}
                  message={errormessage}
                />
              ) : (
                <UserHeader
                  text1={
                    // @ts-ignore
                    uDetails.firstName
                      ? // @ts-ignore
                        `${uDetails.firstName || "N/A"}${
                          uDetails?.middleName
                            ? ` ${uDetails.middleName || "N/A"}`
                            : ""
                        } ${
                          // @ts-ignore
                          uDetails.lastName ? uDetails.lastName || "N/A" : ""
                        }`
                      : ""
                  }
                  showStatus={false}
                  // text1="Welcome"
                  text1Style={{
                    fontSize: 12,
                    color: "#fff",
                    lineHeight: 18,
                    marginBottom: 0,
                    fontFamily: fonts.poppinsRegular,
                  }}
                  icon1={svg.barCodeW}
                  icon2={isNewNoti ? svg.activeNotificationw : svg.notiw}
                  bottomComponent={
                    <View style={{ flexDirection: "row" }}>
                      {uDetails.username ? (
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            gap: 4,
                          }}
                        >
                          {uDetails?.role !== "user" && (
                            <SvgXml xml={svg.badge} />
                          )}
                          <P
                            style={{
                              fontSize: 12,
                              color: "rgba(224, 209, 255, 1)",
                              fontFamily: fonts.poppinsMedium,
                            }}
                          >
                            {isRefCopied
                              ? "Username Copied"
                              : `@${uDetails?.username || ""}`}
                          </P>
                          <TouchableOpacity
                            onPress={() => {
                              copyAccNum();
                            }}
                          >
                            <View
                              style={{
                                width: 20,
                                height: 20,
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              <SvgXml
                                xml={
                                  isRefCopied ? svg.circleSuccess : svg.copy_pr
                                }
                              />
                            </View>
                          </TouchableOpacity>
                        </View>
                      ) : (
                        <></>
                      )}
                    </View>
                  }
                  onPress={() => {
                    navigation.navigate("NotificationScreen");
                  }}
                  brCodePressed={() => {
                    setSHowQrCode(true);
                  }}
                  imgPressed={() => navigation.navigate("Settings")}
                />
              )}

              <TabView
                navigationState={{ index, routes }}
                renderScene={renderScene}
                onIndexChange={setIndex}
                initialLayout={initialLayout}
                swipeEnabled={Platform.OS === "ios" ? isSwipEnabled : false}
                renderTabBar={(props) => {
                  // We need to handle the props carefully to avoid the key warning
                  // TypeScript doesn't know about the key prop, but it might be there at runtime
                  return (
                    <View
                      style={{
                        width: "100%",
                        alignItems: "center",
                        justifyContent: "space-around",
                        alignSelf: "center",
                        marginTop: 8,
                        marginBottom: 10,
                      }}
                    >
                      <TabBar
                        // Safely spread props without the key
                        // @ts-ignore - We're handling a React internal warning
                        {...(props as any)}
                        indicatorStyle={{
                          backgroundColor: colors.white,
                          height: 30,
                          elevation: 0,
                          borderRadius: 100,
                          borderBottomWidth: 0,
                          position: "absolute",
                          alignItems: "center",
                          justifyContent: "center",
                          bottom: 10,
                        }}
                        renderLabel={({ route, focused }) => {
                          return (
                            <P
                              style={{
                                fontSize: 11,
                                color: focused
                                  ? colors.primary
                                  : "rgba(241, 235, 255, 1)",
                              }}
                            >
                              {route.title}
                            </P>
                          );
                        }}
                        style={{
                          backgroundColor: "transparent",
                          justifyContent: "space-between",
                          // width: "60%",
                          width: "50%",
                          alignSelf: "flex-start",
                          marginLeft: "5%",
                        }}
                      />
                    </View>
                  );
                }}
              />
            </>
          )}
        </Div>
      </LinearGradient>

      <BottomSheet
        isVisible={isModalVisible}
        backspaceText="Account overview"
        showBackArrow={false}
        onClose={() => setModalVisible(false)}
        onBackArrowPress={() => setModalVisible(false)}
        components={
          <>
            <ScrollView>
              <View style={styles.wrap}>
                <View style={styles.wrapItem}>
                  <Image
                    source={require("../assets/usFlag.png")}
                    style={styles.flagImg}
                  />
                  <View>
                    <P style={styles.wrapItemText}>United state dollar</P>
                    {hideBal ? (
                      <P style={styles.amount}>
                        ****** <P style={styles.wrapItemText}>***</P>
                      </P>
                    ) : (
                      <P style={styles.amount}>
                        ${formatToTwoDecimals(amount)}
                        <P style={styles.wrapItemText}>USD</P>
                      </P>
                    )}
                  </View>
                </View>
                <View style={styles.wrapItem}>
                  <Image source={curFlag} style={styles.flagImg} />
                  <View>
                    <P style={styles.wrapItemText} numberOfLines={1}>
                      {cur}
                    </P>
                    {hideBal ? (
                      <P style={styles.amount}>
                        ****** <P style={styles.wrapItemText}>***</P>
                      </P>
                    ) : (
                      <P style={styles.amount}>
                        {curSymbol}
                        {/* @ts-ignore */}
                        {formatToTwoDecimals(
                          uDetails.homeCountry === "Turkey" ||
                            //@ts-ignore
                            uDetails.homeCountry === "North-Cyprus"
                            ? trAmount
                            : localAmount
                        )}
                        <P style={styles.wrapItemText}>{curCode}</P>
                      </P>
                    )}
                  </View>
                </View>
              </View>
              {wallets?.length > 0 && (
                <>
                  <NoteComponent2
                    text={`The united state dollar and ${cur} balance is the total sum of your tether and united state coin`}
                  />
                  <P
                    style={{
                      color: colors.gray,
                      marginTop: 24,
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Available dollar account
                  </P>
                  <View style={[styles.wrap, { marginTop: 16 }]}>
                    {wallets.map((item, index) => {
                      return (
                        <View style={[styles.wrapItem]} key={index}>
                          <Image
                            source={
                              item.asset === "USDT"
                                ? require("../assets/tether.png")
                                : require("../assets/usdc.png")
                            }
                            style={styles.flagImg}
                          />
                          <View>
                            <P style={styles.wrapItemText}>
                              {item.asset === "USDT"
                                ? "Tether USD"
                                : "United state dollar coin"}
                            </P>
                            {hideBal ? (
                              <P style={styles.amount}>
                                ****** <P style={styles.wrapItemText}>***</P>
                              </P>
                            ) : (
                              <P style={styles.amount} numberOfLines={1}>
                                {item?.balance?.toLocaleString(undefined, {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}
                                <P style={styles.wrapItemText}>{item.asset}</P>
                              </P>
                            )}
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </>
              )}
            </ScrollView>
          </>
        }
      />

      <BottomSheet
        isVisible={isBottomSheetVisible}
        backspaceText="Edit quicks action"
        showBackArrow={false}
        onClose={() => setBottomSheetVisible(false)}
        onBackArrowPress={() => setBottomSheetVisible(false)}
        extraModalStyle={{ height: "58%" }}
        modalContentStyle={{ height: "60%" }}
        components={
          <>
            <ScrollView>
              <View
                style={{
                  marginTop: 24,
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                {/* <NoteComponent2
                  text={
                    "The united state dollar and Turkish lira balance is the total sum of your tether and united state coin"
                  }
                /> */}
              </View>
              <View
                style={{
                  marginTop: 16,
                  marginBottom: 40,
                }}
              >
                {/* @ts-ignore */}
                <P style={[styles.currencyCode]}>Select quick action option</P>
                <View
                  style={{
                    marginTop: 8,
                    flexDirection: "row",
                    justifyContent: "space-between",
                    flexWrap: "wrap",
                  }}
                >
                  {allActiveBillItems.map((item, index) => {
                    // Find the index of the selected item
                    const selectedIndex = selectedItems.findIndex(
                      (selectedItem) => selectedItem.text === item.text
                    );

                    return (
                      <View
                        key={index}
                        // @ts-ignore
                        style={{
                          width: 100 / 3 + "%",
                          marginBottom: allActiveBillItems?.length > 3 ? 16 : 0,
                        }}
                      >
                        <TouchableOpacity
                          onPress={() => handleItemSelection(item)}
                        >
                          <View
                            style={{
                              width: 93,
                              alignItems: "center",
                              paddingTop: 8,
                              paddingBottom: 8,
                              borderWidth: 1,
                              borderColor:
                                selectedIndex !== -1
                                  ? colors.primary
                                  : colors.stroke,
                              justifyContent: "center",
                              borderRadius: 8,
                            }}
                          >
                            {selectedIndex !== -1 && (
                              <View style={styles.indexIndicator}>
                                <P
                                  style={{
                                    color: colors.white,
                                    fontSize: 12,
                                  }}
                                >
                                  {selectedIndex + 1}
                                </P>
                              </View>
                            )}
                            <SvgXml xml={item.svg} />
                            <P
                              style={{
                                fontSize: 12,
                                marginTop: 8,
                                lineHeight: 18,
                              }}
                            >
                              {item.text}
                            </P>
                          </View>
                        </TouchableOpacity>
                      </View>
                    );
                  })}
                </View>
                <View
                  style={{
                    width: "80%",
                    alignSelf: "center",
                    marginTop: 16,
                  }}
                >
                  <Button
                    btnText="Save"
                    onPress={handleSave}
                    disabled={isSaveDisabled}
                  />
                </View>
              </View>
            </ScrollView>
          </>
        }
      />
      {!isPushNotificationEnabled && !showWelcomeModal && (
        <EnableNotification
          onclose={() => {
            setIsPushNotificationEnabled(true);
          }}
        />
      )}
      {showQrCode && (
        <BarCodeScanner
          visible={showQrCode}
          onScan={handleScan}
          onClose={() => setSHowQrCode(false)}
        />
      )}
      {/* {showWelcomeModal && (

      )} */}
    </>
  );
}

function ActionButton({ text, svg, onPress = () => {} }) {
  return (
    <TouchableOpacity style={styles.actionButton} onPress={onPress}>
      <SvgXml xml={svg} />
      <P style={styles.actionButtonText}>{text}</P>
    </TouchableOpacity>
  );
}

// Commented out unused function
// function BillItem({ text, svg, onPress }: any) {
//   return (
//     <TouchableOpacity onPress={onPress}>
//       <View style={styles.billItem}>
//         <SvgXml xml={svg} />
//         <P style={styles.billText}>{text}</P>
//       </View>
//     </TouchableOpacity>
//   );
// }
const styles = StyleSheet.create({
  gradient: {
    width,
    height,
    flex: 1,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    width: "90%",
    alignSelf: "center",
    marginBottom: 24,
    // elevation: 4,
  },
  modal: {
    // backgroundColor: 'red',
    justifyContent: "flex-end",
    margin: 0,
    height,
    position: "absolute",
  },
  modalContent: {
    height: "50%",
    backgroundColor: "rgba(247, 244, 255, 1)",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  addMoneyButton: {
    // marginTop: 10,
    // paddingVertical: 8,
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "rgba(140, 82, 255, 1)",
    borderRadius: 20,
  },
  addMoneyText: {
    color: "#fff",
    fontSize: 12,
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  recommendation: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  recommendationImage: {
    width: 58,
    height: 58,
    // backgroundColor: "gray",
  },
  recommendationText: {
    // marginLeft: 10,
  },
  recommendationTitle: {
    fontSize: 14,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },
  recommendationMessage: {
    fontSize: 12,
    color: "rgba(78, 130, 76, 1)",
    width: 218,
    // paddingRight: 40,
  },
  recommendationReview: {
    fontSize: 12,
    color: "rgba(78, 130, 76, 1)",
    textDecorationLine: "underline",
  },
  bills: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  billItem: {
    alignItems: "center",
  },
  billIcon: {
    width: 40,
    height: 40,
    backgroundColor: "gray",
    borderRadius: 20,
  },
  billText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  transactions: {},
  transactionsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  transactionItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 20,
    justifyContent: "space-between",
  },
  transactionFlag: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "gray",
  },
  transactionInfo: {
    marginLeft: 10,
  },
  transactionAmount: {
    fontSize: 12,
    fontFamily: fonts.poppinsMedium,
    // fontWeight: "bold",
  },
  transactionDate: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  tab: {
    alignSelf: "center",
    width: "90%",
    // backgroundColor: "red",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
    marginBottom: 12,
  },
  tabBtn: {
    width: 83,
    height: 24,
    marginRight: 12,
    borderRadius: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  actBtn: {
    width: 83,
    height: 24,
    backgroundColor: "#fff",
    marginRight: 12,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  tabBtnP: {
    fontSize: 10,
    lineHeight: 18,
    fontFamily: fonts.poppinsMedium,
  },
  section1: {
    height: 65,
    width: "100%",
    borderBottomColor: "rgba(139, 144, 154, 0.25)",
    borderBottomWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 18,
  },
  priceSelect: {
    width: "100%",
    height: 68,
    backgroundColor: "white",
    marginTop: 16,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingLeft: 32,
    paddingRight: 32,
    borderWidth: 1,
  },
  extraModal: {
    width: "80%",
    height: "52%",
    position: "absolute",
    alignSelf: "center",
    borderRadius: 20,
    backgroundColor: "#fff",
  },
  renderComponent: {
    // width: '90%',
  },
  accItem: {
    width: "48%",
    flexDirection: "row",
    paddingTop: 8,
    paddingBottom: 8,
    alignItems: "center",
  },
  flag: {
    width: 24,
    height: 24,
    marginRight: 9,
  },
  currencyText: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  currencyAmount: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: fonts.poppinsMedium,
  },
  currencyCode: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  indexIndicator: {
    position: "absolute",
    width: 20,
    height: 20,
    backgroundColor: colors.primary,
    zIndex: 10,
    top: -1,
    right: -1,
    borderTopRightRadius: 10,
    borderBottomLeftRadius: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  wrap: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 32,
    marginBottom: 24,
  },
  wrapItem: {
    width: "50%",
    flexDirection: "row",
    alignItems: "center",
  },
  flagImg: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  wrapItemText: {
    fontSize: 12,
    color: colors.gray,
    fontFamily: fonts.poppinsRegular,
  },
  amount: {
    fontSize: 12,
  },
  newBalCard: {
    width: 220,
    height: 124,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
  },
  hText: {
    fontSize: 12,
    color: colors.dGray,
    marginTop: 16,
    fontFamily: fonts.poppinsRegular,
  },
  emptyCur: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
  },
  imgSy: {
    width: 24,
    height: 24,
    borderRadius: 100,
  },
});
