import React, { use<PERSON><PERSON>back, useContext, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import MicroBtn from "../../components/MicroBtn";
import { colors } from "../../config/colors";
import DetailCard from "../../components/DetailCard";
import Button from "../../components/Button";
import Content from "../../components/Content";
import Content2 from "../../components/Content2";
import Input from "../../components/Input";
import { Switch } from "react-native-gesture-handler";
import CustomSwitch from "../../components/CustomSwitch";
import BarCodeScanner from "../../components/BarCodeScanner";
import { SendSFXMoneyApp } from "../../RequestHandlers/Wallet";
import {
  AddBeneficiary,
  GetBeneficiaries,
  GetUserByName,
} from "../../RequestHandlers/User";
import ContentLoader, { Rect } from "react-content-loader/native";
import Loader from "../../components/ActivityIndicator";
import CustomSwitch1 from "../../components/CustomSwitch1";
import { useFocusEffect } from "@react-navigation/native";
import { ensureHttps } from "../../components/AddHttp";
import { useToast } from "../../context/ToastContext";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { CredentailsContext } from "../../RequestHandlers/CredentailsContext";
import {
  addRecentlyUsedBeneficiary,
  mergeWithRecentlyUsed,
} from "../../Utils/recentlyUsedBeneficiariesCache";

const { width, height } = Dimensions.get("window");

export default function AccountDetailsEntry({ navigation, route }) {
  const [isEnabled, setIsEnabled] = useState(false);
  const { handleToast } = useToast();
  const [showQrCode, setShowQrCode] = useState(false);
  const [bene, setBene] = useState(false);
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);
  const [nameError, setNameError] = useState(false);
  const [loadng, setLoading] = useState(false);
  const [username, setUsername] = useState("");
  const [note, setNote] = useState("");
  const [details, setdetails] = useState<any>([]);
  const { data } = route?.params || "";
  const [isKycDone, setIsKycDone] = useState("false");
  const [loader, setLoader] = useState(false);
  const [beneficiaries, setBeneficiaries] = useState<any>([]);
  const [isOn, setIsOn] = useState(false);
  const [ld, setLd] = useState(false);
  const checkBeneficiary = () => {
    setBene(true);
  };
  const { storedCredentails } = useContext(CredentailsContext);

  const isEmpty = (string: string) => {
    if (
      string == "" ||
      string == " " ||
      string == null ||
      string == undefined
    ) {
      return false;
    } else {
      return true;
    }
  };

  const [error, setError] = useState("");
  const [debouncedUsername, setDebouncedUsername] = useState(username); // Debounced username

  const getUser = async (username) => {
    setLoading(true);
    setError("");
    try {
      const getUser = await withApiErrorToast(
        GetUserByName(username),
        handleToast
      );
      if (username === storedCredentails.user.username) {
        setError("Username not found.");
        return;
      }
      if (getUser.username) {
        setLoading(false);
        setIsKycDone(getUser?.verified);
        setBene(true);
        setdetails(getUser);
        setError(""); // Clear error if user is found
      } else if (username.length === 0) {
        setLoading(false);
      } else {
        setLoading(false);
        setBene(false);
        setError("Username not found."); // Set error if username not found
      }
    } catch (error) {
      setLoading(false);
      setError("An error occurred. Please try again."); // Error handling
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const handler = setTimeout(() => {
      if (debouncedUsername) {
        getUser(debouncedUsername);
      }
    }, 500); // Wait 500ms before triggering the API call
    return () => {
      clearTimeout(handler);
    };
  }, [debouncedUsername]);

  useEffect(() => {
    if (data != "" && data != undefined && data != null) {
      getUser(data);
      setUsername(data);
    }
  }, [data != "" && data != undefined && data != null]);
  const handleInputChange = (text) => {
    setUsername(text);
    setError(""); // Clear error when typing
    setDebouncedUsername(text); // Set the debounced value
    setNameError(false);
  };
  const handleScan = (index) => {
    getUser(index);
    setUsername(index);
  };

  const sendSFXMoneyApp = async () => {
    if (isEmpty(username) === false) {
      setNameError(true);
    } else {
      setNameError(false);

      // NOTE: Not adding to recently used cache for manually entered usernames
      // Only cache when user selects from existing beneficiaries list
      // if (details && details.username) {
      //   await addRecentlyUsedBeneficiary({
      //     id: details.id || `sfx-${details.username}`,
      //     account: details.username,
      //     name: `${details.firstName} ${details.lastName}`,
      //     type: "sfx-money-app",
      //     providerName: "SFx money app",
      //     country: "NG",
      //     sfxBeneficiaryUser: {
      //       id: details.id,
      //       username: details.username,
      //       firstName: details.firstName,
      //       lastName: details.lastName,
      //       picture: details.picture,
      //       verified: details.verified,
      //     },
      //   });
      // }

      navigation.navigate("AmountScreen", {
        username: username,
        note: note,
        details: details,
      });
    }
  };

  const getBeneficiaries = async () => {
    setLoader(true);
    try {
      const response = await withApiErrorToast(
        GetBeneficiaries(1, 10, "sfx-money-app"),
        handleToast
      );
      if (response.items) {
        // Merge with recently used beneficiaries (recently used appear first)
        const mergedBeneficiaries = await mergeWithRecentlyUsed(response.items);
        setBeneficiaries(mergedBeneficiaries);
        setLoader(false);
      } else {
        setLoader(false);
        handleToast(response.message, "error");
      }
    } catch (error) {
      setLoader(false);
      console.error("Error fetching beneficiaries:", error);
    }
  };

  const AddBene = async () => {
    try {
      const body = {
        account: details?.username,
        providerName: "SFx money app",
        name: `${details?.firstName} ${details?.lastName}`,
        type: "sfx-money-app",
        country: "NG",
      };
      const response = await withApiErrorToast(
        AddBeneficiary(body),
        handleToast
      );
      if (response.account) {
        handleToast("Beneficiary saved", "success");

        // NOTE: Not adding to recently used cache for newly added KYC beneficiaries
        // Only cache when user selects from existing beneficiaries list
        // await addRecentlyUsedBeneficiary({
        //   id: response.id || `sfx-${details.username}`,
        //   account: details.username,
        //   name: `${details.firstName} ${details.lastName}`,
        //   type: "sfx-money-app",
        //   providerName: "SFx money app",
        //   country: "NG",
        //   sfxBeneficiaryUser: {
        //     id: details.id,
        //     username: details.username,
        //     firstName: details.firstName,
        //     lastName: details.lastName,
        //     picture: details.picture,
        //     verified: details.verified,
        //   },
        // });

        // Refresh beneficiaries list to show the newly added one
        getBeneficiaries();
      } else {
        handleToast(response.message, "error");
        setIsOn(false);
      }
    } catch (error) {
      console.error("Error adding beneficiary:", error);
    }
  };

  const handleSwitch = (state) => {
    if (isOn) {
      setIsOn(false);
    } else {
      setIsOn(true);
      if (state) {
        if (details.length === 0) {
          handleToast("No beneficiary to save", "error");
          setTimeout(() => {
            setIsOn(false);
          }, 1000);
        } else if (isKycDone === "false") {
          handleToast("You cant't save an unverified user", "error");
          setTimeout(() => {
            setIsOn(false);
          }, 1000);
        } else {
          AddBene();
        }
      } else {
      }
    }
  };

  const getUserByName = async (uName) => {
    setLd(true);
    try {
      const res = await withApiErrorToast(GetUserByName(uName), handleToast);
      if (res.verified === "true") {
        // Add to recently used cache when selecting from beneficiaries list
        await addRecentlyUsedBeneficiary({
          id: res.id || `sfx-${res.username}`,
          account: res.username,
          name: `${res.firstName} ${res.lastName}`,
          type: "sfx-money-app",
          providerName: "SFx money app",
          country: "NG",
          sfxBeneficiaryUser: {
            id: res.id,
            username: res.username,
            firstName: res.firstName,
            lastName: res.lastName,
            picture: res.picture,
            verified: res.verified,
          },
        });

        navigation.navigate("AmountScreen", {
          username: res.username,
          note: note,
          details: res,
        });
        setLd(false);
      } else {
        handleToast("You cant't send money to an unverified user", "error");
        setLd(false);
      }
    } catch (error) {
      setLd(false);
      console.error("Error getting user by name:", error);
    }
  };

  // useEffect(() => {
  //   getBeneficiaries();
  // }, []);
  useFocusEffect(
    useCallback(() => {
      getBeneficiaries();
    }, [])
  );
  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="SFx money app" navigation={navigation} />
        <View>
          <ScrollView automaticallyAdjustKeyboardInsets={true}>
            <View style={styles.contentBody}>
              <View style={styles.detailWrap}>
                <Input
                  label="Username"
                  placeholder="Mato123"
                  inputStyle={{ width: "85%" }}
                  // contStyle={{ marginBottom: 16 }}
                  value={username}
                  error={nameError}
                  rightIcon={
                    <TouchableOpacity
                      onPress={() => {
                        setShowQrCode(true);
                      }}
                    >
                      <SvgXml xml={svg.qrcode} />
                    </TouchableOpacity>
                  }
                  onChangeText={handleInputChange} // Call the handler on text change
                />
                {nameError && (
                  <View style={{ width: "100%" }}>
                    <P
                      style={{
                        color: colors.red,
                        marginTop: 8,
                        textAlign: "left",
                        fontSize: 12,
                      }}
                    >
                      Username is required
                    </P>
                  </View>
                )}
                {error ? (
                  <View style={{ width: "100%" }}>
                    <P
                      style={{
                        color: colors.red,
                        marginTop: 8,
                        textAlign: "left",
                        fontSize: 12,
                      }}
                    >
                      {error}
                    </P>
                  </View>
                ) : null}
                {loadng ? (
                  <ContentLoader
                    style={{ marginBottom: 16, marginTop: 16 }}
                    width={"100%"}
                    height={44}
                    speed={2}
                    backgroundColor="#F7F4FF"
                    foregroundColor="#ecebeb"
                  >
                    {/* The Rect now covers the entire height of the loader */}
                    <Rect x="0" y="0" rx="4" ry="4" width="100%" height="44" />
                  </ContentLoader>
                ) : (
                  bene && (
                    <View style={styles.benefeciary}>
                      {/* @ts-ignore */}
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "flex-start",
                          maxWidth: 220,
                          overflow: "hidden",
                        }}
                      >
                        <Image
                          // @ts-ignore
                          source={
                            details.picture
                              ? { uri: ensureHttps(details.picture) }
                              : require("../../assets/defualtAvatar.png")
                          }
                          style={{
                            width: 30,
                            height: 30,
                            borderRadius: 100,
                            marginRight: 8,
                          }}
                        />
                        {/* @ts-ignore */}
                        <P>{`${details?.firstName} ${details?.lastName}`}</P>
                      </View>
                      {isKycDone === "true" ? (
                        <SvgXml xml={svg.green_check} />
                      ) : (
                        <P style={{ fontSize: 11, color: colors.red }}>
                          Unverified
                        </P>
                      )}
                    </View>
                  )
                )}
                <Input
                  label={
                    <P style={styles.label}>
                      Note {/* @ts-ignore */}
                      <P style={[styles.label, { color: colors.gray }]}>
                        (Optional)
                      </P>
                    </P>
                  }
                  placeholder="Enter note here..."
                  contStyle={{ marginBottom: 16, marginTop: 16 }}
                  onChangeText={(text) => setNote(text)}
                  value={note}
                />
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    width: "100%",
                    // marginBottom: 16,
                  }}
                >
                  <CustomSwitch1 onToggle={handleSwitch} isOn={isOn} />
                  <P
                    style={{
                      color: "#A5A1A1",
                      marginLeft: 8,
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Save Beneficiary
                  </P>
                </View>
              </View>
              <View style={{ width: "80%", marginTop: 32 }}>
                <Button
                  btnText="Next"
                  loading={ld}
                  onPress={() => {
                    if (
                      username === "" ||
                      username === undefined ||
                      username === null
                    ) {
                      setNameError(true);
                    } else if (isKycDone === "true") {
                      sendSFXMoneyApp();
                    } else {
                      handleToast(
                        "You cant't send money to an unverified user",
                        "error"
                      );
                    }
                  }}
                />
              </View>

              <View style={styles.detailWrap2}>
                <View style={styles.deatilsHead}>
                  <P
                    style={{
                      color: "#A5A1A1",
                      fontSize: 12,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Beneficiaries
                  </P>

                  {beneficiaries.length > 0 && (
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                      }}
                    >
                      <TouchableOpacity
                        onPress={() =>
                          navigation.navigate("Beneficiaries", {
                            actT: "sfx-money-app",
                          })
                        }
                      >
                        <P
                          style={{
                            color: "#8B52FF",
                            textDecorationLine: "underline",
                            alignItems: "center",
                            fontSize: 12,
                          }}
                        >
                          See all
                        </P>
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                {beneficiaries.length === 0 ? (
                  <View
                    style={{ alignItems: "center", justifyContent: "center" }}
                  >
                    <SvgXml
                      xml={svg.userGroup}
                      style={{ marginTop: 52, marginBottom: 16 }}
                    />
                    <P
                      style={{
                        fontFamily: fonts.poppinsMedium,
                        marginBottom: 4,
                        fontSize: 12,
                      }}
                    >
                      No beneficiary!
                    </P>
                    <P
                      style={{
                        color: "#A5A1A1",
                        fontFamily: fonts.poppinsRegular,
                        fontSize: 12,
                      }}
                    >
                      You have no benefeciary yet
                    </P>
                  </View>
                ) : (
                  <>
                    {beneficiaries.slice(0, 4).map((i, index) => (
                      <TouchableOpacity
                        key={index}
                        style={{ width: "100%" }}
                        onPress={() => {
                          getUserByName(i.account);
                        }}
                      >
                        <View
                          style={{
                            width: "100%",
                            marginTop: 18,
                            flexDirection: "row",
                            paddingLeft: 24,
                            paddingRight: 24,
                            alignItems: "center",
                          }}
                        >
                          {i?.sfxBeneficiaryUser?.picture ? (
                            <Image
                              source={{
                                uri: ensureHttps(
                                  i?.sfxBeneficiaryUser?.picture
                                ),
                              }}
                              style={{
                                width: 36,
                                height: 36,
                                objectFit: "cover",
                                borderRadius: 100,
                              }}
                            />
                          ) : (
                            <SvgXml xml={svg.smalllogo} />
                          )}

                          <View style={{ marginLeft: 16, flex: 1 }}>
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                              }}
                            >
                              <P
                                style={{
                                  fontSize: 12,
                                  marginBottom: 4,
                                  flex: 1,
                                }}
                              >
                                {i?.name}
                              </P>
                            </View>
                            <P
                              style={{
                                fontSize: 12,
                                lineHeight: 18,
                                color: colors.gray,
                                fontFamily: fonts.poppinsRegular,
                              }}
                            >
                              {i?.account} | SFx money app
                            </P>
                          </View>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </>
                )}
              </View>
            </View>
          </ScrollView>
        </View>
        {showQrCode && (
          <BarCodeScanner
            visible={showQrCode}
            onScan={handleScan}
            onClose={() => {
              setShowQrCode(false);
            }}
          />
        )}
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
    // backgroundColor: "#fff",
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: 24,
    paddingBottom: 24,
    // justifyContent:"center",
    alignItems: "center",
  },
  benefeciary: {
    paddingTop: 4,
    paddingRight: 16,
    paddingBottom: 4,
    paddingLeft: 16,
    backgroundColor: "#F7F4FF",
    borderRadius: 8,
    flexDirection: "row",
    width: "100%",
    minHeight: 44,
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
    marginTop: 16,
  },
  deatilsHead: {
    width: "100%",
    height: 42,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "5%",
  },
  detailWrap: {
    padding: 24,
    width: "90%",
    alignSelf: "center",
    // height:200,
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  detailWrap2: {
    // padding: 24,
    width: "90%",
    alignSelf: "center",
    minHeight: 246,
    backgroundColor: "white",
    borderRadius: 12,
    // justifyContent: "center",
    alignItems: "center",
    marginTop: 60,
    paddingBottom: 20,
  },

  desCont: {
    width: "100%",
  },
  items: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  buttonWrap: {
    width: "80%",
    alignSelf: "center",
    marginTop: 32,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
});
