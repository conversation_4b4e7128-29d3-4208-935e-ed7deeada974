import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { colors } from "../../config/colors";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import P from "../../components/P";
import QRCode from "react-native-qrcode-svg";
import NoteComponent2 from "../../components/NoteComponent2";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import * as Clipboard from "expo-clipboard";
import Input from "../../components/Input";
import BottomSheet from "../../components/BottomSheet";
import { GetWalletById } from "../../RequestHandlers/Wallet";
import { WalletDetailsSkeleton } from "../../Skeletons/Skeletons";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import { useToast } from "../../context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function WalletDetails({ navigation, route }) {
  const [isRefCopied, setIsRefCopied] = useState(false);
  const [wallAdd, setWallAdd] = useState("");
  const [network, setNetwork] = useState("");
  const [showNetwork, setShowNetwork] = useState(false);
  const [activeNetwork, setActiveNetwork] = useState(null);
  const [loader, setLoader] = useState(false);
  const { data } = route.params || {};
  const [wallets, setWallets] = useState<any>([]);
  const [chain, setChain] = useState("...");
  const [allNetwork, setAllNetwork] = useState([]);
  const [selectedNetworkDetails, setSelectedNetworkDetails] = useState(null);
  const { handleToast } = useToast();
  const copyAccNum = async () => {
    await Clipboard.setStringAsync(wallAdd);
    setIsRefCopied(true);
    setTimeout(() => {
      setIsRefCopied(false);
    }, 4000);
  };

  const truncateAddress = (
    address,
    startChars = 6,
    endChars = 6,
    mask = "*****"
  ) => {
    if (address.length <= startChars + endChars) {
      return address;
    }
    const start = address.slice(0, startChars);
    const end = address.slice(-endChars);
    return `${start}${mask}${end}`;
  };

  // const chainNameMapping = {
  //   CHAIN_CELO: "Celo",
  //   CHAIN_TRON: "Tron-TRC 20",
  //   CHAIN_POLYGON: "Polygon",
  // };

  // const dummyNetworks = [
  //   { name: "Celo", arrival: "Money arrival in 3 minutes" },
  //   { name: "Tron-TRC 20", arrival: "Money arrival in 5 minutes" },
  //   { name: "Polygon", arrival: "Coming soon" },
  //   { name: "BSC", arrival: "Coming soon" },
  // ];

  const getWalById = async () => {
    try {
      const walletDetails = await withApiErrorToast(
        GetWalletById(data.id),
        handleToast
      );
      if (walletDetails.wallet) {
        setLoader(false);
        setWallets(walletDetails.wallet);
        setAllNetwork(walletDetails.wallet.assets);
        const firstAsset = walletDetails.wallet.assets[0];
        const name = firstAsset.chain.split("_");
        const networkName = name[1];
        setWallAdd(firstAsset.depositAddress);
        setChain(networkName || "...");
        setActiveNetwork(firstAsset.chain);
      } else {
      }
    } catch (error) {}
  };

  useEffect(() => {
    setLoader(true);
    getWalById();
  }, []);

  return (
    <View style={styles.body}>
      <Div>
        {loader ? (
          WalletDetailsSkeleton()
        ) : (
          <>
            <AuthenticationHedear
              text="Wallet details"
              navigation={navigation}
            />
            <ScrollView>
              <View style={styles.body}>
                <View style={styles.detailWrap}>
                  <P
                    style={{
                      fontSize: 12,
                      lineHeight: 19.5,
                      color: colors.gray,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Send money to SFx money app
                  </P>
                  <P style={{ marginBottom: 16 }}>
                    Scan QR code to make payment
                  </P>
                  <QRCode
                    value={wallAdd || "..."}
                    size={(20 * height) / 100}
                    color="black"
                    logo={
                      wallets.asset === "USDT"
                        ? require("../../assets/tether.png")
                        : require("../../assets/usdc.png")
                    }
                    logoSize={40}
                    logoBorderRadius={5}
                    backgroundColor="white"
                  />
                  {/* <P
                    style={{
                      padding: 8,
                      paddingTop: 4,
                      paddingBottom: 4,
                      backgroundColor: colors.secBackground,
                      borderRadius: 99,
                      marginTop: (2 * height) / 100,
                      fontSize: 10,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    Network arrival in
                    <P
                      style={{
                        fontSize: 10,
                        color: colors.primary,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      {" "}
                      3 minutes
                    </P>
                  </P> */}
                  <View style={styles.line}></View>
                  <View style={{ width: "100%" }}>
                    <View style={[styles.items, { marginBottom: 0 }]}>
                      <SvgXml xml={svg.wallet2} style={{ marginRight: 8 }} />
                      <View>
                        <P style={styles.holder}>Wallet address</P>
                        <P style={styles.value}>
                          {wallAdd ? truncateAddress(wallAdd) : "..."}
                        </P>
                      </View>
                      <TouchableOpacity
                        onPress={copyAccNum}
                        style={styles.copyBtn}
                      >
                        <View style={styles.copyBtn}>
                          <P style={styles.copyText}>
                            {isRefCopied ? "Copied" : "Copy"}
                          </P>
                          <SvgXml
                            xml={isRefCopied ? svg.circleSuccess : svg.copy}
                            style={{ width: 14, height: 14 }}
                          />
                        </View>
                      </TouchableOpacity>
                    </View>
                    <TouchableOpacity
                      disabled={allNetwork.length === 1}
                      onPress={() => setShowNetwork(true)}
                    >
                      <Input
                        value={chain}
                        label="Network"
                        placeholder={chain}
                        inputStyle={{ width: "60%", color: "#161817" }}
                        contStyle={{ marginTop: 16 }}
                        editable={false}
                        rightIcon={
                          allNetwork.length < 2 ? (
                            <></>
                          ) : (
                            <View
                              style={{
                                width: "15%",
                                height: "100%",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                            >
                              <SvgXml xml={svg.dropDown} />
                            </View>
                          )
                        }
                      />
                    </TouchableOpacity>
                    <View style={{ marginTop: 24 }}>
                      <NoteComponent2
                        contStyle={{ paddingRight: 50 }}
                        text={`Please note that minimum deposit is 1 ${
                          // @ts-ignore
                          wallets.asset
                        }. The current address only supports adding ${
                          //@ts-ignore
                          wallets.asset
                        } on ${chain}, adding other assets will result in loss`}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </ScrollView>
          </>
        )}
      </Div>
      <BottomSheet
        isVisible={showNetwork}
        showBackArrow={false}
        backspaceText="Network"
        onClose={() => setShowNetwork(false)}
        modalContentStyle={{ height: "55%" }}
        extraModalStyle={{ height: "53%" }}
        components={
          <ScrollView>
            <View style={{ paddingTop: 24 }}>
              <P
                style={{
                  fontSize: 12,
                  color: colors.gray,
                  fontFamily: fonts.poppinsRegular,
                }}
              >
                Select a network from assets to send as money
              </P>
              <View>
                {allNetwork.map((item, index) => {
                  const name = item.chain.split("_");
                  const networkName = name[1];

                  return (
                    <TouchableOpacity
                      key={index}
                      onPress={() => {
                        setActiveNetwork(item.chain);
                        setNetwork(item.name);
                        setShowNetwork(false);
                        setChain(networkName);
                        setWallAdd(item?.depositAddress);
                        // setSelectedNetworkDetails(networkDetails);
                        // setChain(networkDetails.chain);
                      }}
                    >
                      <View
                        style={{
                          width: "100%",
                          padding: 16,
                          paddingTop: 16,
                          paddingBottom: 16,
                          borderRadius: 8,
                          // marginBottom: 16,
                          marginTop: 6,
                          backgroundColor:
                            activeNetwork === item.chain
                              ? colors.lowOpPrimary2
                              : "transparent",
                        }}
                      >
                        <P style={{ fontSize: 12, lineHeight: 18 }}>
                          {networkName}
                        </P>
                        {/* <P
                          style={{
                            fontSize: 12,
                            color: colors.gray,
                            fontFamily: fonts.poppinsRegular,
                          }}
                        >
                          {item.arrival}
                        </P> */}
                      </View>
                    </TouchableOpacity>
                  );
                })}
                <View style={{ marginTop: 32 }}>
                  <NoteComponent2
                    text={`Please note that minimum Deposit/ withdrawal is $1 USDT/USDC for CELO and $10 USDT/USDC for TRON. Always make sure you confirm asset and Network`}
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.lowOpPrimary2,
  },
  contentBody: {
    width,
    height: (100 * height) / 100,
    backgroundColor: colors.lowOpPrimary2,
    paddingTop: (3.5 * height) / 100,
    paddingBottom: (3.5 * height) / 100,
    alignItems: "center",
  },
  detailWrap: {
    padding: (3.5 * height) / 100,
    width: "90%",
    alignSelf: "center",
    backgroundColor: "white",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  line: {
    width: "100%",
    marginTop: (3.5 * height) / 100,
    marginBottom: (3.5 * height) / 100,
    borderBottomWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
  },
  holder: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.gray,
    marginBottom: 4,
    fontFamily: fonts.poppinsRegular,
  },
  value: {
    fontSize: 12,
    lineHeight: 18,
    color: colors.black,
  },
  items: {
    width: "100%",
    flexDirection: "row",
    padding: 8,
    alignItems: "center",
    marginBottom: 8,
  },
  copyBtn: {
    paddingTop: 4,
    paddingBottom: 4,
    padding: 13,
    backgroundColor: colors.lowOpPrimary2,
    position: "absolute",
    right: 0,
    borderRadius: 99,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  copyText: {
    fontSize: 10,
    lineHeight: 16,
    marginRight: 4,
  },
});
