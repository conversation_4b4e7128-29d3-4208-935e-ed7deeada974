import { StyleSheet, TouchableOpacity, View } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import NoteComponent2 from "../../components/NoteComponent2";
import Input from "../../components/Input";
import Button from "../../components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";

export default function BillingInfoScreen({ navigation }) {
  return (
    <View style={styles.cont}>
      <Div>
        <AuthenticationHeader
          text="Billing information"
          navigation={navigation}
        />
        <View style={{ alignItems: "center" }}>
          <SvgXml xml={svg.map} />
        </View>
        <View
          style={{
            width: "90%",
            alignSelf: "center",
            paddingVertical: 18,
            paddingHorizontal: 16,
            backgroundColor: colors.white,
            marginTop: 32,
            borderRadius: 12,
          }}
        >
          <View style={styles.item}>
            <View style={styles.value}>
              <P style={styles.valueText}>Country</P>
            </View>
            <View style={styles.it}>
              <TouchableOpacity>
                <SvgXml xml={svg.lightCopy} />
              </TouchableOpacity>
              <P style={styles.itText}>Nigeria</P>
            </View>
          </View>
          <View style={styles.item}>
            <View style={styles.value}>
              <P style={styles.valueText}>State/province</P>
            </View>
            <View style={styles.it}>
              <TouchableOpacity>
                <SvgXml xml={svg.lightCopy} />
              </TouchableOpacity>
              <P style={styles.itText}>Lagos</P>
            </View>
          </View>
          <View style={styles.item}>
            <View style={styles.value}>
              <P style={styles.valueText}>Postal code</P>
            </View>
            <View style={styles.it}>
              <TouchableOpacity>
                <SvgXml xml={svg.lightCopy} />
              </TouchableOpacity>
              <P style={styles.itText}>000000</P>
            </View>
          </View>
          <View style={styles.item}>
            <View style={styles.value}>
              <P style={styles.valueText}>City</P>
            </View>
            <View style={styles.it}>
              <TouchableOpacity>
                <SvgXml xml={svg.lightCopy} />
              </TouchableOpacity>
              <P style={styles.itText}>Ojo</P>
            </View>
          </View>
          <View style={styles.item}>
            <View style={styles.value}>
              <P style={styles.valueText}>Address</P>
            </View>
            <View style={styles.it}>
              <TouchableOpacity>
                <SvgXml xml={svg.lightCopy} />
              </TouchableOpacity>
              <P style={styles.itText}>
                14, Block A, Longson dormitory, eastern Mediterranean
                university, Famagusta, North Cyprus
              </P>
            </View>
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  item: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  it: {
    width: "70%",
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
    gap: 4,
  },
  itText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
    textAlign: "right"
  },
  value: {
    width: "30%",
    flexDirection: "row",
  },
  valueText: {
    fontSize: 12,
    fontFamily: fonts.poppinsRegular,
  },
});
