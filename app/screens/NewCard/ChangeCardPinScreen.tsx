import React, { useRef, useState, useEffect, useContext } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
  Keyboard,
  TextInput,
  TouchableOpacity,
  ImageBackground,
} from "react-native";
import Div from "../../components/Div";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import Input from "../../components/Input";
import BottomComponent from "../../components/BottomComponent";
import * as yup from "yup";
import { Formik } from "formik";
import { ResetPassword } from "../../RequestHandlers/Authentication";
import i18n from "../../../i18n";
import { GetUserDetails, ResetPin } from "../../RequestHandlers/User";
import { useToast } from "../../context/ToastContext";
import { encryptPIN } from "../../Utils/encrypt";
import * as SecureStore from "expo-secure-store";
import { FingerPrintStatus } from "../../context/FingerPrintContext";
import AuthHeader from "../../components/AuthHeader";
import H4 from "../../components/H4";
import { withApiErrorToast } from "../../Utils/withApiErrorToast";
import AuthenticationHeader from "../../components/AuthenticationHedear";

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
const screenHeight = Dimensions.get("window").height;
export default function ChangeCardPinScreen({ navigation, route }) {
  const { handleToast } = useToast();
  const [loading, setLoading] = useState(false);
  const ref_input1 = useRef();
  const ref_input2 = useRef();
  const ref_input3 = useRef();
  const ref_input4 = useRef();
  const ref_input5 = useRef();
  const ref_input6 = useRef();
  const ref_input7 = useRef();
  const ref_input8 = useRef();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const refs = [ref_input1, ref_input2, ref_input3, ref_input4];
  const refs2 = [ref_input5, ref_input6, ref_input7, ref_input8];
  const [fields, setFields] = useState(["", "", "", ""]);
  const [fields2, setFields2] = useState(["", "", "", ""]);
  const [show, setShow] = useState(true);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [activeIndex2, setActiveIndex2] = useState<number | null>(null);
  const [showEnabler, setShowEnabler] = useState(false);
  const [id, setId] = useState("");
  const { storedPrivateKey, setStoredPrivateKey } =
    useContext(FingerPrintStatus);

  const focusNextField = (nextField: any) => {
    nextField.current.focus();
  };
  const focusNextField2 = (nextField: any) => {
    nextField.current.focus();
  };

  const handleKeyPress = (index: any, event: any) => {
    const { key, nativeEvent } = event;
    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs[prevIndex]);
        }
      } else {
        setFields((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };
  const handleKeyPress2 = (index: any, event: any) => {
    const { key, nativeEvent } = event;
    if (nativeEvent.key === "Backspace" || nativeEvent.key === "Delete") {
      if (fields2[index] === "") {
        const prevIndex = index - 1;
        if (prevIndex >= 0) {
          setFields2((prevFields) => {
            const updatedFields = [...prevFields];
            updatedFields[prevIndex] = "";
            return updatedFields;
          });
          focusNextField(refs2[prevIndex]);
        }
      } else {
        setFields2((prevFields) => {
          const updatedFields = [...prevFields];
          updatedFields[index] = "";
          return updatedFields;
        });
      }
    }
  };
  const handleChangeText = (index: any, text: any) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField(refs[nextIndex]);
        } else {
          if (index === updatedFields.length - 1) {
            Keyboard.dismiss();
          }
        }
      }
      return updatedFields;
    });
  };
  const handleChangeText2 = (index: any, text: any) => {
    setFields2((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;
      if (text !== "") {
        const nextIndex = index + 1;
        if (nextIndex < updatedFields.length) {
          focusNextField2(refs2[nextIndex]);
        } else {
          if (index === updatedFields.length - 1) {
            Keyboard.dismiss();
          }
        }
      }
      return updatedFields;
    });
  };
  const handlePaste = (index: any, pastedText: string) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      const characters = pastedText.split("");
      for (let i = 0; i < characters.length; i++) {
        const fieldIndex = index + i;
        if (fieldIndex < updatedFields.length) {
          updatedFields[fieldIndex] = characters[i];
        }
      }

      return updatedFields;
    });
  };
  const handleFocus2 = (index: number) => {
    setActiveIndex2(index);
  };
  const handleBlur2 = () => {
    setActiveIndex2(null);
  };
  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };
  const handleBlur = () => {
    setActiveIndex(null);
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    if (
      fields.every((field) => field !== "") &&
      fields2.every((field2) => field2 !== "")
    ) {
      navigation.pop(2);
    } else {
    }
  };

  const getInputBorderStyle = (index) => {
    return {
      borderColor: fields[index] === "" && isSubmitted ? colors.red : "#E6E5E5",
    };
  };
  const getInputBorderStyle2 = (index) => {
    return {
      borderColor:
        fields2[index] === "" && isSubmitted ? colors.red : "#E6E5E5",
    };
  };
  const getUserId = async () => {
    try {
      const res = await GetUserDetails();
      if (res.id) {
        setId(res.id);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getUserId();
  }, []);
  return (
    <View style={styles.body}>
      <Div style={{ height: screenHeight }}>
        <View
          style={{
            width: "85%",
            marginTop: 16,
            flexDirection: "row",
            justifyContent: "flex-end",
            alignSelf: "center",
          }}
        >
          <TouchableOpacity
            style={{
              paddingHorizontal: 12,
              paddingVertical: 7,
              borderRadius: 99,
              borderWidth: 1,
              borderColor: colors.stroke,
            }}
            onPress={() => {
              navigation.pop();
            }}
          >
            <P style={{ fontSize: 12 }}>Close</P>
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.container}>
          {/* <AuthHeader style={{ width: "90%" }} navigation={navigation}  /> */}

          <View
            style={{
              width: "85%",
              justifyContent: "center",
              alignSelf: "center",
              marginTop: 8,
              alignItems: "center",
            }}
          >
            <SvgXml xml={svg.padLock} />
            <H4 style={styles.text1}>Change card PIN</H4>
            <P style={styles.text2}>
              Create a transaction PIN to secure your card
            </P>
          </View>
          <View style={styles.components}>
            <View
              style={{
                paddingVertical: 16,
                backgroundColor: colors.white,
                borderRadius: 12,
              }}
            >
              <View style={styles.section3Wrap}>
                <P
                  style={{
                    fontSize: 12,
                    marginBottom: 8,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  New Card PIN
                </P>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      style={[
                        styles.pinInput,
                        {
                          borderColor:
                            activeIndex === index
                              ? colors.primary
                              : getInputBorderStyle(index).borderColor,
                        },
                      ]}
                      key={index}
                    >
                      <TextInput
                        style={styles.pinTextInput}
                        placeholderTextColor="#000"
                        keyboardType="numeric"
                        ref={ref}
                        onChangeText={(text) => handleChangeText(index, text)}
                        onKeyPress={(event) => handleKeyPress(index, event)}
                        // @ts-ignore
                        onTextInput={(event) => {
                          const pastedText = event.nativeEvent.text;
                          handlePaste(index, pastedText);
                        }}
                        value={fields[index]}
                        secureTextEntry={show}
                        onFocus={() => handleFocus(index)}
                        onBlur={handleBlur}
                      />
                    </View>
                  ))}
                </View>
              </View>
              <View style={styles.section3Wrap}>
                <P
                  style={{
                    fontSize: 12,
                    marginBottom: 8,
                    textAlign: "center",
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Confirm new card PIN
                </P>
                <View style={styles.con}>
                  {refs2.map((ref, index) => (
                    <View
                      style={[
                        styles.pinInput,
                        {
                          borderColor:
                            activeIndex2 === index
                              ? colors.primary
                              : getInputBorderStyle2(index).borderColor,
                        },
                      ]}
                      key={index}
                    >
                      <TextInput
                        style={styles.pinTextInput}
                        placeholderTextColor="#000"
                        keyboardType="numeric"
                        ref={ref}
                        onChangeText={(text) => handleChangeText2(index, text)}
                        onKeyPress={(event) => handleKeyPress2(index, event)}
                        // @ts-ignore
                        onTextInput={(event) => {
                          const pastedText = event.nativeEvent.text;
                          handlePaste(index, pastedText);
                        }}
                        value={fields2[index]}
                        secureTextEntry={show}
                        onFocus={() => handleFocus2(index)}
                        onBlur={handleBlur2}
                      />
                    </View>
                  ))}
                </View>
              </View>
            </View>
            <View style={{ marginTop: 32, width: "85%", alignSelf: "center" }}>
              <Button
                btnText="Continue"
                onPress={handleSubmit}
                loading={loading}
              />
            </View>
          </View>
        </ScrollView>
        {/* <ImageBackground
          source={require("../../assets/background.png")}
          style={{
            position: "absolute",
            bottom: 0,
            width: "100%",
            height: 120,
          }}
        /> */}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsSemibold,
    lineHeight: 30,
    textAlign: "center",
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
  },
  components: {
    width: "85%",
    marginTop: 30,
    alignSelf: "center",
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
  section3Wrap: {
    width: "100%",
    backgroundColor: colors.white,
  },
  con: {
    flexDirection: "row",
    width: "100%",
    alignSelf: "center",
    justifyContent: "center",
    gap: 8,
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 56,
    height: 56,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  pinTextInput: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
    width: 56,
    height: 56,
  },
});
