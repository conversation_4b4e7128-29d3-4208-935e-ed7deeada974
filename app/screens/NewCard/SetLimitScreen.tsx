import { StyleSheet, View } from "react-native";
import { colors } from "../../config/colors";
import Div from "../../components/Div";
import AuthenticationHeader from "../../components/AuthenticationHedear";
import NoteComponent2 from "../../components/NoteComponent2";
import Input from "../../components/Input";
import Button from "../../components/Button";

export default function SetLimitScreen({ navigation }) {
  return (
    <View style={styles.cont}>
      <Div>
        <AuthenticationHeader text="Set limit" navigation={navigation}/>
        <View style={{ width: "90%", marginTop: 16, alignSelf: "center" }}>
          <NoteComponent2
            type="red"
            contStyle={{ backgroundColor: colors.white }}
            text={
              "The daily limit for your account tier 2, card and in-app transaction is $1,000"
            }
          />
        </View>
        <View
          style={{
            width: "90%",
            alignSelf: "center",
            paddingVertical: 18,
            paddingHorizontal: 16,
            backgroundColor: colors.white,
            marginTop: 16,
            borderRadius: 12,
          }}
        >
          <Input label={"Maximum limit"} />
        </View>
        <View style={{ width: "75%", alignSelf: "center", marginTop: 32 }}>
          <Button
            btnText="Continue"
            onPress={() => {
              navigation.pop();
            }}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  cont: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
});
