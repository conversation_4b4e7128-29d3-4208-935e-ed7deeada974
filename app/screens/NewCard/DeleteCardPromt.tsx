import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import { colors } from "../../config/colors";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import Link from "../../components/Link";
import NoteComponent2 from "../../components/NoteComponent2";
import Div from "../../components/Div";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import DropDown from "../../components/DropDown";

const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function DeleteCardPromt({ navigation, route }) {
  const [stImg, setStImg] = useState(require("../../assets/alertError.png"));
  const [stText, setStText] = useState("Sent money is pending");
  const { cardID } = route?.params || "";

  const reasons = [
    { id: 1, lable: "Lost device", value: "lost-device" },
    { id: 2, lable: "Account compromised", value: "account-compromised" },
    { id: 3, lable: "Unauthorized access", value: "unauthorized-access" },
    { id: 4, lable: "Not using temporarily", value: "Not-using-temporarily" },
    { id: 5, lable: "Others", value: "Other" },
  ];
  return (
    <View style={styles.body}>
      <Div>
        <View
          style={{
            width: "85%",
            marginTop: 16,
            flexDirection: "row",
            justifyContent: "flex-end",
            alignSelf: "center",
          }}
        >
          <TouchableOpacity
            style={{
              paddingHorizontal: 12,
              paddingVertical: 7,
              borderRadius: 99,
              borderWidth: 1,
              borderColor: colors.stroke,
            }}
            onPress={() => {
              navigation.pop();
            }}
          >
            <P style={{ fontSize: 12 }}>Close</P>
          </TouchableOpacity>
        </View>
        <View style={{ width: "85%", alignSelf: "center", marginTop: 8 }}>
          <NoteComponent2
            type="red"
            contStyle={{ backgroundColor: colors.redSubtle }}
            text={
              "Instantly freeze your card to stop all transactions — without cancelling it"
            }
          />
        </View>
        <View style={styles.itemBox}>
          <SvgXml xml={svg.LargeTrash} />
          <P style={styles.statusState}>Permanently delete card?</P>

          <P style={styles.stTx}>
            Your card number is 0000 **** 5555, will be deleted from your SFx
            account and:
          </P>
          <View
            style={{
              width: "85%",
              alignSelf: "center",
              backgroundColor: colors.secBackground,
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 12,
              marginTop: 12,
            }}
          >
            {[
              "The card will no longer be available for transactions",
              "All related transaction history will be permanently deleted",
              "This action cannot be undone",
            ].map((it, id) => (
              <View
                key={id}
                style={{
                  marginTop: id == 0 ? 0 : 4,
                  flexDirection: "row",
                  gap: 8,
                }}
              >
                <P>•</P>
                <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
                  {it}
                </P>
              </View>
            ))}
          </View>
          <View style={{ width: "85%", marginTop: 24 }}>
            <DropDown
              label="Reason for deleting card"
              data={reasons}
              headerText="Reasons"
            />
          </View>
          <View style={{ width: "75%", marginTop: 32 }}>
            <Button
              btnText="Proceed"
              onPress={() => {
                navigation.navigate("DeleteCardPin", { cardID: cardID });
              }}
            />
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    width,
    flex: 1,
    backgroundColor: colors.white,
  },
  itemBox: {
    width: "100%",
    alignItems: "center",
    marginTop: 21,
  },
  statusState: {
    fontSize: 20,
    lineHeight: 24,
    textAlign: "center",
    marginTop: 16,
    fontFamily: fonts.poppinsSemibold,
  },
  stTx: {
    width: "90%",
    fontSize: 13,
    lineHeight: 19.2,
    textAlign: "center",
    fontFamily: fonts.poppinsRegular,
    color: colors.dark500,
    marginTop: 4,
  },
});
