import React, { useCallback, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import { colors } from "../../config/colors";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import Content from "../../components/Content";
import Button from "../../components/Button";
import CustomSwitch from "../../components/CustomSwitch";
import VirtualCard from "../../components/VirtualCard";
import { GetcardById, GetcardByToken } from "../../RequestHandlers/Card";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../components/ActivityIndicator";
import CardHolder from "./components/CardHolder";

const { width, height } = Dimensions.get("window");
const baseHeight = 812;
export default function CardMoreScreen({ navigation, route }) {
  const [loader, setLoader] = useState(false);
  const { details } = route?.params || {};
  const [items, setItems] = useState<any>([]);
  const [mainD, setMainD] = useState<any>([]);
  const [exYear, setExYear] = useState("");
  //   const [freezeCardPin, setFreezeCardPin] = useState(false);
  const [isCardFreezed, setIsCardFreezed] = useState(false);
  const handleToggle = (newState) => {
    if (newState) {
      navigation.navigate("FreezeCardPin", {
        cardID: items?.card?.id,
        status: true,
        activityType: "freeze-card",
      });
      setIsCardFreezed(true);
    } else {
      navigation.navigate("FreezeCardPin", {
        cardID: items?.card?.id,
        status: false,
        activityType: "unfreeze-card",
      });
      setIsCardFreezed(false);
    }
  };

  function formatCardNumber(cardNumber) {
    const cleaned = cardNumber?.replace(/\D+/g, "");
    const formatted = cleaned.match(/.{1,4}/g)?.join(" ") || "";
    return formatted;
  }

  return (
    <View style={styles.body}>
      <Div>
        <AuthenticationHedear text="More" navigation={navigation} />
        <ScrollView contentContainerStyle={{ paddingBottom: 50 }}>
          {/* <View style={styles.cc}></View> */}
          <CardHolder showBal={false} holderStyle={{ marginTop: 0 }} />
          <View style={styles.actionCard}>
            <Content
              svg1={svg.cFreeze}
              header={isCardFreezed ? "Unfreeze card" : "Freeze card"}
              arrowRight={false}
              disabled={true}
              rightComponent={
                <CustomSwitch onToggle={handleToggle} isOn={isCardFreezed} />
              }
            />
            <Content
              svg1={svg.billing}
              header="Billing"
              onPress={() =>
                navigation.navigate("BillingInfoScreen")
              }
            />
            <Content
              svg1={svg.newLimit}
              header="Set limit"
              onPress={() => navigation.navigate("SetLimitScreen")}
            />
            <Content
              svg1={svg.securityLock}
              isCardFreezed={isCardFreezed}
              header="Change PIN"
              onPress={
                () =>
                  navigation.navigate("ValidatePinScreen", {
                    headerText: "Change card Pin",
                  })
                // navigation.navigate("ChangeCardPinScreen", { cardID: items?.card?.id })
              }
            />
            {/* <Content
              svg1={svg.cardG}
              isCardFreezed={isCardFreezed}
              header="Change card color"
              onPress={() =>
                navigation.navigate("CardColor", { cardID: items?.card?.id })
              }
            /> */}
            <Content
              svg1={svg.alertError2}
              header="FAQs"
              //   onPress={() => navigation.navigate("MobileMoneyScreen2")}
            />
            <Content
              svg1={svg.support}
              header="Support"
              bottomBorder={false}
              //   onPress={() => navigation.navigate("DeleteCardPromt")}
            />
          </View>
          <View
            style={{
              width: "90%",
              marginTop: (16 / baseHeight) * height,
              alignSelf: "center",
            }}
          >
            <Button
              onPress={() =>
                navigation.navigate("DeleteCardPromt", {
                  cardID: items?.card?.id,
                })
              }
              style={{
                backgroundColor: colors.white,
                borderRadius: 12,
                height: 48,
              }}
              btnTextStyle={{ color: colors.red }}
              btnText="Delete card"
            />
          </View>
          {/* transactions */}
        </ScrollView>
      </Div>
      {loader && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  card: {
    // backgroundColor: 'red',¿
    borderRadius: 12,
    padding: 16,
    width: "90%",
    alignSelf: "center",
    marginBottom: 12,
    // elevation: 4,
  },
  accountBalance: {
    alignItems: "flex-start",
    // backgroundColor:"red",
  },
  balanceText: {
    fontSize: 12,
    color: "rgba(22, 24, 23, 0.6)",
  },
  balanceAmount: {
    fontSize: 24,
    // fontWeight: "bold",
    fontFamily: "poppins-semibold",
    color: "rgba(22, 24, 23, 1)",
  },

  cc: {
    width: "90%",
    alignSelf: "center",
    alignItems: "center",
  },
  actionCard: {
    width: "90%",
    borderRadius: 12,
    alignSelf: "center",
    backgroundColor: colors.white,
    alignItems: "center",
    marginTop: 16,
  },
});
