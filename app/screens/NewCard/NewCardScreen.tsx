import {
  ActivityIndicator,
  Dimensions,
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { colors } from "../../config/colors";
import { useState } from "react";
import Div from "../../components/Div";
import H4 from "../../components/H4";
import P from "../../components/P";
import { fonts } from "../../config/Fonts";
import Button from "../../components/Button";
import GradientText from "../../components/GradientText";
import { useNavigation } from "@react-navigation/native";
import Svg, { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import { hide } from "expo-splash-screen";
import { Card } from "./components/Card";
import TransactionItem from "../../components/TransactionItem";
import { formatDate } from "../../components/FormatDate";
import CardHolder from "./components/CardHolder";
const { width, height } = Dimensions.get("window");

export default function NewCardScreen({ navigation }) {
  const [hasCard, setHasCard] = useState(true);

  const [isCardFreezed, setIsCardFreezed] = useState(false);
  const [loader1, setLoader1] = useState(false);

  function capitalizeFirstLetter(word) {
    if (!word) return ""; // handle empty strings
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  }

  const transactions = [];
  const renderNoCardSatet = () => {
    return (
      <View
        style={{
          width: "90%",
          alignSelf: "center",
          minHeight: (90 * height) / 100,
          justifyContent: "center",
        }}
      >
        <View
          style={{ alignItems: "center", width: "80%", alignSelf: "center" }}
        >
          <GradientText style={{ textAlign: "center" }}>
            Empower your finances
          </GradientText>
          <H4
            style={{
              fontSize: 24,
              fontFamily: fonts.poppinsSemibold,
              lineHeight: 28.8,
              marginTop: 8,
            }}
          >
            with the SFx Card
          </H4>
          <P style={{ fontFamily: fonts.poppinsRegular, textAlign: "center" }}>
            Your gateway to seamless transactions and unmatched convenience
          </P>
        </View>
        <Image
          source={require("../../assets/CardMock.webp")}
          style={{
            width: (80 * width) / 100,
            height: (55 * height) / 100,
            objectFit: "fill",
            borderRadius: 12,
            marginTop: (5 * height) / 100,
          }}
        />
        <Button
          btnText="Get virtual card"
          style={{ width: "90%", alignSelf: "center", marginTop: -20 }}
          onPress={() => {
            navigation.navigate("CardPurchaseInfoScreen");
          }}
        />
      </View>
    );
  };

  return (
    <View style={styles.body}>
      <Div>
        {hasCard && (
          <View style={styles.cardHeader}>
            <H4 style={{ fontFamily: fonts.poppinsSemibold }}>Card</H4>
            <TouchableOpacity style={styles.ncBtn}>
              <SvgXml width={16} height={16} xml={svg.pPlus} />
              <P style={styles.nc}>New card</P>
            </TouchableOpacity>
          </View>
        )}
        <ScrollView
          style={{ width: "100%" }}
          contentContainerStyle={{ paddingBottom: 120 }}
          showsVerticalScrollIndicator={false}
        >
          {!hasCard ? (
            renderNoCardSatet()
          ) : (
            <>
              <CardHolder />
              <View style={styles.actions}>
                <ActionButton
                  text="Details"
                  isCardFreezed={isCardFreezed}
                  svg={svg.cardDetails}
                />
                <ActionButton
                  text="Transer"
                  svg={svg.transfer}
                  isCardFreezed={isCardFreezed}
                />
                <ActionButton
                  text="Wallet"
                  svg={svg.walletRed}
                  isCardFreezed={isCardFreezed}
                />
                <ActionButton
                  text="More"
                  svg={svg.more}
                  isCardFreezed={false}
                  onPress={() => {
                    navigation.navigate("CardMoreScreen");
                  }}
                />
              </View>
              {loader1 ? (
                <View style={[styles.card]}>
                  <ActivityIndicator size="large" color={colors.primary} />
                </View>
              ) : (
                <View
                  style={[
                    styles.card,
                    {
                      padding: 0,
                      minHeight: 246,
                      marginBottom: 100,
                      backgroundColor: colors.white,
                    },
                  ]}
                >
                  {transactions.length > 4 && (
                    <View
                      style={{
                        width: "100%",
                        borderBottomWidth: 1,
                        borderColor: "rgba(240, 239, 239, 1)",
                        flexDirection: "row",
                        justifyContent: "space-between",
                        height: 45,
                        alignItems: "center",
                        paddingHorizontal: 16,
                      }}
                    >
                      <P style={{ color: colors.dark500, fontSize: 12 }}>
                        Recent transaction
                      </P>

                      <TouchableOpacity
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                        onPress={() => {
                          navigation.navigate("CardHistory");
                        }}
                      >
                        <P
                          style={{
                            fontSize: 12,
                            marginRight: 5,
                            color: colors.primary,
                            textDecorationLine: "underline",
                          }}
                        >
                          See all
                        </P>
                      </TouchableOpacity>
                    </View>
                  )}

                  <View style={[]}>
                    {transactions?.length === 0 ? (
                      <View
                        style={{
                          width: "100%",
                          alignItems: "center",
                          justifyContent: "center",
                          height: 246,
                        }}
                      >
                        <SvgXml
                          xml={svg.noTransaction}
                          style={{ marginBottom: 16 }}
                        />
                        <P
                          style={{
                            fontFamily: fonts.poppinsMedium,
                            fontSize: 12,
                          }}
                        >
                          No transaction!
                        </P>
                        <P
                          style={{
                            color: colors.dGray,
                            fontFamily: fonts.poppinsRegular,
                            fontSize: 12,
                          }}
                        >
                          You have no transaction yet
                        </P>
                      </View>
                    ) : (
                      transactions?.slice(0, 4)?.map((item, index) => (
                        <TransactionItem
                          key={index}
                          onPress={() => {
                            navigation.navigate("CardTransactionDetails", {
                              id: item.id,
                            });
                          }}
                          icon={
                            item?.type === "fund-card"
                              ? svg.ccaddFill
                              : item?.type === "withdraw-from-card"
                              ? svg.withdraw
                              : item?.type === "create-card"
                              ? svg.cardF
                              : item?.type === "card-debit"
                              ? svg.bb
                              : item?.type === "card-transaction-reversal"
                              ? svg.bb
                              : svg.bankGreen
                          }
                          itemStyle={{
                            borderColor: colors.stroke,
                            alignItems: "center",
                            paddingLeft: 16,
                            paddingRight: 16,
                          }}
                          amount={`${
                            item?.type === "fund-card" ||
                            item?.type === "create-card"
                              ? "+"
                              : "-"
                          }$${
                            item?.amount
                              ? item?.amount?.toFixed(2).toLocaleString()
                              : "..."
                          }`}
                          title={
                            item?.type === "fund-card"
                              ? "Card top-up"
                              : item?.type === "withdraw-from-card"
                              ? "Card withdrawal"
                              : item?.type === "create-card"
                              ? "Card payment"
                              : item?.type === "card-transaction-reversal"
                              ? "Reversal"
                              : "Spent USD"
                          }
                          date={formatDate(item?.updatedAt)}
                          status={
                            item.status === "completed"
                              ? "Successful"
                              : item?.status === "processing"
                              ? "Pending"
                              : capitalizeFirstLetter(item?.status)
                          }
                          statusStyle={{
                            textAlign: "right",
                            color: item.status
                              .toLowerCase()
                              .includes("completed")
                              ? colors.green
                              : item.status.toLowerCase().includes("pending") ||
                                item.status.toLowerCase().includes("processing")
                              ? colors.yellow
                              : colors.red,
                          }}
                        />
                      ))
                    )}
                  </View>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </Div>
    </View>
  );
}

function ActionButton({ text, svg, onPress = () => {}, isCardFreezed }) {
  return (
    <TouchableOpacity
      style={[styles.actionButton, { opacity: isCardFreezed ? 0.5 : 1 }]}
      onPress={onPress}
      disabled={isCardFreezed ? true : false}
    >
      <SvgXml xml={svg} width={24} height={24} />
      <P style={styles.actionButtonText}>{text}</P>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: colors.secBackground,
  },
  cardHeader: {
    width: "90%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    alignSelf: "center",
    marginTop: 16,
  },
  ncBtn: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  nc: {
    color: colors.primary,
    fontFamily: fonts.poppinsRegular,
    textDecorationLine: "underline",
  },

  actionButton: {
    alignItems: "center",
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 12,
    color: "rgba(22, 24, 23, 1)",
  },
  actions: {
    width: "90%",
    alignSelf: "center",
    backgroundColor: colors.white,
    flexDirection: "row",
    justifyContent: "space-around",
    padding: 16,
    marginTop: 16,
    borderRadius: 12,
  },
  card: {
    width: "90%",
    alignSelf: "center",
    marginTop: 16,
    borderRadius: 12,
  },
});
