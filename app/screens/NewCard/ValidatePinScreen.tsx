import React, { useEffect, useRef, useState, useContext } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  // Keyboard,
  Text,
  Platform,
} from "react-native";
import { fonts } from "../../config/Fonts";
import Div from "../../components/Div";
import AuthenticationHedear from "../../components/AuthenticationHedear";
import { SvgXml } from "react-native-svg";
import { svg } from "../../config/Svg";
import P from "../../components/P";
import Button from "../../components/Button";
import { colors } from "../../config/colors";
import SendMoneyStatus from "../../components/SeendMoneyStatus";
import Keyboard2 from "../../components/Keyboard2";
import Keyboard from "../../components/Keyboard";
import { ValidatePin } from "../../RequestHandlers/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { GetUserDetails } from "../../RequestHandlers/User";
import * as LocalAuthentication from "expo-local-authentication";
import { FingerPrintStatus } from "../../context/FingerPrintContext";
import EnableBiomatricComponent from "../../components/EnableBiomatricComponent";
import { TransactionAuth } from "../../components/TransactionAuth";
import { MomoSendMoney } from "../../RequestHandlers/Wallet";
import {
  DeleteCard,
  FreezeCard,
  PayForCard,
  UnFreezeCard,
  WithdrawFromCard,
} from "../../RequestHandlers/Card";
import { useToast } from "../../context/ToastContext";
import { encryptPIN } from "../../Utils/encrypt";
import NewKeyboard from "../../components/AppKeyboard";
const baseHeight = 802;
const { width, height } = Dimensions.get("window");

export default function ValidatePinScreen({ navigation, route }) {
  const [showSendStatus, setShowSendStatus] = useState(false);
  const { cardID, status, headerText } = route?.params || "";

  const [fields, setFields] = useState(["", "", "", ""]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showDots, setShowDots] = useState([false, false, false, false]);
  const refs = [useRef(), useRef(), useRef(), useRef()];
  const [loading, setLoading] = useState(false);
  const { details, amount, walletId } = route.params || "";
  const { handleToast } = useToast();
  const [invalidFields, setInvalidFields] = useState([
    false,
    false,
    false,
    false,
  ]);
  const [isBioMatricSupported, setIsBioMatricSupported] = useState(true);
  const [showEnabler, setShowEnabler] = useState(false);
  const [id, setId] = useState("");
  const {
    storedFingerPrintStatus,
    setStoredFingerPrintStatus,
    storedPrivateKey,
  } = useContext(FingerPrintStatus);

  const handleKeyPress = (key) => {
    if (key === "←") {
      if (activeIndex > 0 || fields[activeIndex] !== "") {
        handleChangeText(activeIndex, "");
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[activeIndex] = false;
          return updatedDots;
        });
        if (activeIndex > 0) {
          setActiveIndex(activeIndex - 1);
        }
      }
    } else if (key === "Enter") {
      // Handle enter key press
    } else {
      handleChangeText(activeIndex, key);
      if (activeIndex < 3) {
        setActiveIndex(activeIndex + 1);
      }
    }
  };

  const handleChangeText = (index, text) => {
    setFields((prevFields) => {
      const updatedFields = [...prevFields];
      updatedFields[index] = text;

      if (text !== "") {
        setShowDots((prevShowDots) => {
          const updatedDots = [...prevShowDots];
          updatedDots[index] = false;
          return updatedDots;
        });

        setTimeout(() => {
          setShowDots((prevShowDots) => {
            const updatedDots = [...prevShowDots];
            updatedDots[index] = true;
            return updatedDots;
          });
        }, 500);

        if (index === refs.length - 1) {
          // Keyboard.dismiss();
        }
      }
      return updatedFields;
    });

    // Reset invalid fields if user starts typing
    setInvalidFields((prevInvalidFields) => {
      const updatedInvalidFields = [...prevInvalidFields];
      updatedInvalidFields[index] = false;
      return updatedInvalidFields;
    });
  };
  const deleteCard = async () => {
    setLoading(true);
    try {
      const res = await DeleteCard(cardID);
      if (res.status === true) {
        setLoading(false);
        handleToast(res?.message, "success");
        setTimeout(() => {
          navigation.navigate(3);
        }, 2000);
      } else {
        setLoading(false);
        handleToast(res?.message, "error");
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const validatePin = async (pin) => {
    setLoading(true);
    if (fields.some((field) => field === "" && storedPrivateKey === null)) {
      // Show red borders for empty fields
      setInvalidFields(fields.map((field) => field === ""));
      handleToast("Please fill in all PIN fields", "error");
      return;
    }
    try {
      const body = {
        pin: pin,
        activityType: "delete-card",
      };
      const response = await ValidatePin(body);
      if (!response.status) {
        setInvalidFields([true, true, true, true]);
        handleToast(response.message || "Invalid PIN", "error");
      } else {
        // deleteCard();
        navigation.navigate("ChangeCardPinScreen")
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const handleBiomatricSubmit = async () => {
    if (storedPrivateKey) {
      setLoading(true);
      validatePin(storedPrivateKey);
    }
  };
  const handleSubmit = async () => {
    const encryptedPin = await encryptPIN(fields.join(""));
    validatePin(encryptedPin);
  };
  const getUserDetails = async () => {
    try {
      const userDetails = await GetUserDetails();
      setId(userDetails?.id);
    } catch (error) {}
  };
  const persistBiomatric = () => {
    AsyncStorage.setItem(`fingerPrintStatus${id}`, "true")
      .then(() => {
        // @ts-ignore
        setStoredFingerPrintStatus("true");
        handleBiomatricSubmit();
      })
      .catch((err) => {});
  };

  useEffect(() => {
    getUserDetails();
    async () => {
      const isCompatible = await LocalAuthentication.hasHardwareAsync();
      if (!isCompatible) {
        setIsBioMatricSupported(false);
        throw new Error("Your device isn't compatible.");
      }
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        setIsBioMatricSupported(false);
        throw new Error("No Faces / Fingers found.");
      }
    };
  }, []);

  useEffect(() => {
    if (fields.every((field) => field !== "")) {
      // All fields have been filled, call verifyPin
      // handleSubmit();
    }
  }, [fields]);

  return (
    <>
      <View style={styles.body}>
        <Div>
          <ScrollView>
            <AuthenticationHedear text={headerText} navigation={navigation} />
            <View style={styles.contentBody}>
              <View style={styles.inputCardWrap}>
                <P
                  style={{
                    color: colors.dark500,
                    textAlign: "center",
                    marginBottom: 16,
                    fontSize: 12,
                    fontFamily: fonts.poppinsRegular,
                  }}
                >
                  Enter transaction PIN
                </P>
                <View style={styles.con}>
                  {refs.map((ref, index) => (
                    <View
                      key={index}
                      style={[
                        styles.pinInput,
                        {
                          borderColor: invalidFields[index]
                            ? colors.red // Red border for invalid fields
                            : activeIndex === index
                            ? colors.primary
                            : "#E6E5E5",
                        },
                      ]}
                    >
                      <View style={styles.pinView}>
                        {showDots[index] ? (
                          <View style={styles.dot} />
                        ) : (
                          <Text style={styles.pinText}>{fields[index]}</Text>
                        )}
                      </View>
                    </View>
                  ))}
                </View>
              </View>
              <View style={styles.bottom}>
                <View style={{ width: "90%", alignSelf: "center" }}>
                  <NewKeyboard
                    onKeyPress={handleKeyPress}
                    onBioMatric={() => {
                      if (
                        storedFingerPrintStatus &&
                        storedPrivateKey !== null
                      ) {
                        TransactionAuth(handleBiomatricSubmit, handleToast);
                      } else {
                        setShowEnabler(true);
                      }
                    }}
                    loading={loading}
                    onEnter={handleSubmit}
                  />
                </View>
                {/* <View
                  style={{
                    width: "75%",
                    alignSelf: "center",
                    marginTop: (2 * height) / 100,
                  }}
                >
                  <Button
                    btnText="Enter pin"
                    onPress={handleSubmit}
                    loading={loading}
                  />
                  {isBioMatricSupported && (
                    <>
                      <View style={styles.seprator}>
                        <View style={styles.line}></View>
                        <P
                          style={{
                            fontSize: 12,
                            fontFamily: fonts.poppinsRegular,
                            color: colors.gray,
                          }}
                        >
                          Or
                        </P>
                        <View style={styles.line}></View>
                      </View>
                      <TouchableOpacity
                        style={{
                          width: "100%",
                          alignSelf: "center",
                          alignItems: "center",
                          justifyContent: "center",
                          borderWidth: 1,
                          borderColor: colors.stroke,
                          borderRadius: 100,
                          height: 44,
                          flexDirection: "row",
                          marginTop: (2 * height) / 100,
                        }}
                        onPress={() => {
                          if (
                            storedFingerPrintStatus &&
                            storedPrivateKey !== null
                          ) {
                            TransactionAuth(handleBiomatricSubmit, handleToast);
                          } else {
                            setShowEnabler(true);
                          }
                        }}
                      >
                        <SvgXml
                          xml={
                            Platform.OS === "ios"
                              ? svg.faceIdGray
                              : svg.fingerPrint
                          }
                          style={{ marginRight: 4 }}
                        />
                        <Text
                          style={{
                            marginLeft: 4,
                            color: colors.primary,
                            fontFamily: fonts.poppinsRegular,
                            fontSize: 12,
                          }}
                        >
                          {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
                        </Text>
                      </TouchableOpacity>
                    </>
                  )}
                </View> */}
              </View>
            </View>
          </ScrollView>
        </Div>
        {/* {showSendStatus && (
          <SendMoneyStatus
            okayPress={() => {
              navigation.navigate("Home");
            }}
            viewDetailPress={() =>
              navigation.navigate("AllTransactionDetails", {
                transactionType: "sfx money app",
              })
            }
          />
        )} */}
        {showEnabler && (
          <EnableBiomatricComponent
            visible={showEnabler}
            onClose={() => {
              setShowEnabler(false);
            }}
            secondaryFunction={() => {
              persistBiomatric();
            }}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    backgroundColor: "rgba(247, 244, 255, 1)",
  },
  contentBody: {
    width,
    height: (92 * height) / 100,
    backgroundColor: "rgba(247, 244, 255, 1)",
    paddingTop: 16,
  },
  inputCardWrap: {
    width: "90%",
    alignSelf: "center",
    paddingVertical: 24,
    backgroundColor: "#fff",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  pinInput: {
    borderWidth: 1,
    borderRadius: 8,
    width: 52,
    height: 52,
    alignItems: "center",
    justifyContent: "center",
  },
  pinView: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  pinText: {
    fontSize: 18,
    textAlign: "center",
    color: "#000",
    fontFamily: fonts.poppinsMedium,
  },
  dot: {
    width: 16, // Bigger size for the dot
    height: 16,
    backgroundColor: "#000",
    borderRadius: 12,
  },
  con: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    gap: 16,
  },
  bottom: {
    width,
    flex: 1,
    justifyContent: "flex-end",
    paddingBottom: Math.max(5, height * 0.15),
    marginTop: 36,
  },
  line: {
    width: "43%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  seprator: {
    width: "100%",
    lineHeight: 19,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 16,
  },
});
