import { Dimensions, StyleSheet, View } from "react-native";
import { colorTone } from "react-native-color-matrix-image-filters";
import { colors } from "../../../config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../config/Svg";

const { height } = Dimensions.get("window");
export function Card() {
  return (
    <View style={styles.card}>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <SvgXml xml={svg.sfxWhite} />
        <SvgXml xml={svg.visa} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: "100%",
    height: 180,
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 11.37,
  },
});
