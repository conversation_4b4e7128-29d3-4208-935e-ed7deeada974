import {
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewProps,
  ViewStyle,
} from "react-native";
import P from "../../../components/P";
import { SvgXml } from "react-native-svg";
import { fonts } from "../../../config/Fonts";
import { Card } from "./Card";
import { svg } from "../../../config/Svg";
import { colors } from "../../../config/colors";
import { useState } from "react";

interface PProps {
  showBal?: boolean;
  holderStyle?: StyleProp<ViewStyle>;
}
export default function CardHolder({ showBal = true, holderStyle }: PProps) {
  const [hideBal, setHideBal] = useState(false);
  return (
    <View style={[styles.cardHolder, holderStyle]}>
      {showBal && (
        <>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <P
              style={{
                fontSize: 12,
                lineHeight: 18,
                fontFamily: fonts.poppinsRegular,
              }}
            >
              Total balance
            </P>
            <TouchableOpacity onPress={() => setHideBal(!hideBal)}>
              <SvgXml
                xml={hideBal ? svg.eyeClose : svg.eyeOpen}
                style={{ marginLeft: 8 }}
              />
            </TouchableOpacity>
          </View>
          <P
            style={{
              fontFamily: fonts.poppinsRegular,
              fontSize: 24,
              lineHeight: 28,
              marginTop: 4,
              marginBottom: 10,
            }}
          >
            {hideBal ? "******" : "$0.00"}
            <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
              {hideBal ? "***" : "USD"}
            </P>
          </P>
        </>
      )}
      <View style={{}}>
        <Card />
        <TouchableOpacity style={styles.addressBtn}>
          <SvgXml xml={svg.cardOutline} />
          <P style={{ fontSize: 12, fontFamily: fonts.poppinsRegular }}>
            0x60******db42
          </P>
          <SvgXml xml={svg.chevronDownIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  cardHolder: {
    width: "90%",
    padding: 16,
    backgroundColor: colors.white,
    alignSelf: "center",
    marginTop: 16,
    borderRadius: 12,
  },
  addressBtn: {
    alignSelf: "center",
    marginTop: 10,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: colors.stroke,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
});
