import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Dimensions,
  Text,
  Platform,
  Keyboard,
  Linking,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Div from "../components/Div";
import { colors } from "../config/colors";
import P from "../components/P";
import { fonts } from "../config/Fonts";
import Button from "../components/Button";
import Link from "../components/Link";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import Input from "../components/Input";
import BottomComponent from "../components/BottomComponent";
import SignupHeader from "../components/SignupHeader";
import GoogleBtn from "../components/GoogleBtn";
import { Formik } from "formik";
import * as yup from "yup";
import { SendOtp } from "../RequestHandlers/Authentication";

import AppleBtn from "../components/AppleBtn";
import { useToast } from "../context/ToastContext";
import AuthHeader from "../components/AuthHeader";
import H4 from "../components/H4";
import PasswordValidation from "../components/PasswordValidation";
import { CheckReferralCode } from "../RequestHandlers/User";
import { withApiErrorToast } from "../Utils/withApiErrorToast";

const baseHeight = 800;
const screenHeight = Dimensions.get("window").height;
export default function SignupScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const [initialReferralCode, setInitialReferralCode] = useState("");
  const { handleToast } = useToast();

  // Check for stored referral code from deep linking
  useEffect(() => {
    const checkStoredReferralCode = async () => {
      try {
        const storedCode = await AsyncStorage.getItem("referralCode");
        if (storedCode) {
          setInitialReferralCode(storedCode);
          // Clear the stored code after using it
          await AsyncStorage.removeItem("referralCode");
        }
      } catch (error) {
        console.error("Error checking stored referral code:", error);
      }
    };

    checkStoredReferralCode();
  }, []);

  const registerSchema1 = yup.object().shape({
    email: yup
      .string()
      .email("Invalid email address")
      .required("Email is required"),
    password: yup
      .string()
      .required("Password is required")
      .matches(
        /^(?=.*[0-9])(?=.*[*?!#$%&@^()\-_=+\\|[\]{};:/?.>])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9*?!#$%&@^()\-_=+\\|[\]{};:/?.>]{8,}$/,
        "Password must contain at least 8 characters, including letters, numbers, and special characters, with at least one uppercase letter"
      ),
    confirmPassword: yup
      .string()
      .required("Type your password again")
      .oneOf([yup.ref("password"), null], "Passwords must match"),
    referralCode: yup.string(),
  });

  return (
    <View style={{ flex: 1, backgroundColor: colors.white }}>
      <Div>
        <Formik
          initialValues={{
            email: "",
            password: "",
            confirmPassword: "",
            referralCode: initialReferralCode,
          }}
          enableReinitialize={true}
          validationSchema={registerSchema1}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            try {
              // Check referral code first
              const res = await CheckReferralCode(values.referralCode);
              if (res.error) {
                handleToast(res.message, "error");
                setLoading(false);
                return;
              }
              // Send OTP
              const otpBody = {
                email: values.email,
                type: "verify",
              };
              const sendOtp = await withApiErrorToast(SendOtp(otpBody), handleToast);
              if (sendOtp.error) {
                handleToast(sendOtp.message, "error");
                setLoading(false);
                return;
              }
              // Navigate to verification screen
              navigation.navigate("VerifyEmailScreen2", {
                values1: values,
              });
            } catch (error) {
              console.error("Signup error:", error);
              handleToast("Network error. Please try again.", "error");
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <>
              <ScrollView
                style={styles.container}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{ paddingBottom: 100 }}
                showsVerticalScrollIndicator={false}
              >
                {/* <AuthHeader navigation={navigation} style={{width: "95%"}} /> */}
                <View
                  style={{
                    width: "90%",
                    justifyContent: "center",
                    alignSelf: "center",
                    alignItems: "center",
                    marginTop: 54,
                  }}
                >
                  <H4 style={styles.text1}>Sign up</H4>
                  <P style={styles.text2}>
                    Create a SFx money app{"\n"}account with a few details
                  </P>
                </View>
                <View style={styles.components}>
                  <Input
                    placeholder="<EMAIL>"
                    label="Email"
                    inputStyle={{ width: "85%" }}
                    onChangeText={formikProps.handleChange("email")}
                    value={formikProps.values.email}
                    onBlur={formikProps.handleBlur("email")}
                    autoCapitalize="none"
                    error={
                      formikProps.errors.email && formikProps.touched.email
                    }
                  />
                  {formikProps.errors.email && formikProps.touched.email && (
                    <P style={styles.errorText}>{formikProps.errors.email}</P>
                  )}
                  <Input
                    placeholder="*******"
                    label="New password"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16 }}
                    type={"password"}
                    onChangeText={formikProps.handleChange("password")}
                    value={formikProps.values.password}
                    onBlur={formikProps.handleBlur("password")}
                    error={
                      formikProps.errors.password &&
                      formikProps.touched.password
                    }
                    showPasswordStrength={true}
                  />
                  <PasswordValidation password={formikProps.values.password} />
                  {formikProps.errors.password &&
                    formikProps.touched.password && (
                      <P style={styles.errorText}>
                        {formikProps.errors.password}
                      </P>
                    )}
                  <Input
                    placeholder="*******"
                    label="Confirm new password"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16 }}
                    type={"password"}
                    onChangeText={formikProps.handleChange("confirmPassword")}
                    value={formikProps.values.confirmPassword}
                    onBlur={formikProps.handleBlur("confirmPassword")}
                    error={
                      formikProps.errors.confirmPassword &&
                      formikProps.touched.confirmPassword
                    }
                  />
                  {formikProps.errors.confirmPassword &&
                    formikProps.touched.confirmPassword && (
                      <P style={styles.errorText}>
                        {formikProps.errors.confirmPassword}
                      </P>
                    )}
                  <Input
                    placeholder="Enter referral code"
                    label="Referral Code (optional)"
                    inputStyle={{ width: "85%" }}
                    contStyle={{ marginTop: 16 }}
                    value={formikProps.values.referralCode}
                    onChangeText={formikProps.handleChange("referralCode")}
                  />
                  <P
                    style={{
                      fontSize: 12,
                      lineHeight: 19.2,
                      marginBottom: 32,
                      marginTop: 16,
                      fontFamily: fonts.poppinsRegular,
                    }}
                  >
                    By clicking next you agree to SFx's{" "}
                    <Text
                      style={{
                        textDecorationColor: "#A5A1A1",
                        textDecorationLine: "underline",
                        color: colors.primary,
                        fontFamily: fonts.poppinsMedium,
                      }}
                      onPress={() => {
                        Linking.openURL(
                          "https://sfx-1.gitbook.io/legal-and-policy/privacy-policy-for-sfx"
                        );
                      }}
                    >
                      Privacy Policy
                    </Text>{" "}
                    &{" "}
                    <Text
                      style={{
                        textDecorationColor: "#A5A1A1",
                        textDecorationLine: "underline",
                        color: colors.primary,
                        fontFamily: fonts.poppinsMedium,
                      }}
                      onPress={() => {
                        Linking.openURL(
                          "https://sfx-1.gitbook.io/legal-and-policy/terms-and-conditions"
                        );
                      }}
                    >
                      Terms of Service.
                    </Text>
                  </P>
                  <Button
                    btnText="Next"
                    // onPress={() => }
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                  />
                  <View style={styles.seprator}>
                    <View style={styles.line}></View>
                    <P
                      style={{
                        fontSize: 12,
                        fontFamily: fonts.poppinsRegular,
                        color: colors.gray,
                      }}
                    >
                      Or
                    </P>
                    <View style={styles.line}></View>
                  </View>
                  <GoogleBtn
                    contStyle={{ marginTop: 16 }}
                    navigation={navigation}
                  />
                  {Platform.OS === "ios" && (
                    <View style={{ marginTop: 16 }}>
                      <AppleBtn onPress={() => {}} navigation={navigation} />
                    </View>
                  )}
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "center",
                      marginTop: 32,
                    }}
                  >
                    <P
                      style={{
                        fontSize: 12,
                        lineHeight: 22.4,
                        fontFamily: fonts.poppinsRegular,
                      }}
                    >
                      Do you have an account?{" "}
                    </P>
                    <Link
                      style={{
                        fontSize: 12,
                        lineHeight: 21,
                        textDecorationLine: "underline",
                      }}
                      onPress={() => navigation.navigate("NewLoginScreen")}
                    >
                      Login
                    </Link>
                  </View>
                </View>
              </ScrollView>
              <BottomComponent absolute={true} navigation={navigation} />
            </>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    height: screenHeight,
  },

  text1: {
    fontSize: 20,
    fontFamily: fonts.poppinsBold,
    lineHeight: 30,
  },
  text2: {
    fontSize: 14,
    lineHeight: 22.4,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
    textAlign: "center",
  },
  components: {
    width: "90%",
    marginTop: 24,
    alignSelf: "center",
    marginBottom: Platform.OS === "ios" ? 102 : 68,
    // backgroundColor:"red"
  },
  line: {
    width: "45%",
    height: 1,
    backgroundColor: colors.stroke,
  },
  seprator: {
    width: "100%",
    lineHeight: 19,
    marginTop: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  errorText: {
    fontSize: 12,
    color: colors.red,
    fontFamily: fonts.poppinsRegular,
    marginTop: 4,
  },
});
