import React from "react";
import { Text, TextStyle, ViewStyle, Platform } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { fonts } from "../config/Fonts";

// Conditional import for MaskedView to handle compatibility issues
let MaskedView: any = null;
try {
  MaskedView = require("@react-native-masked-view/masked-view").default;
} catch (error) {
  console.warn("MaskedView not available, falling back to regular text");
}

interface GradientTextProps {
  children: React.ReactNode;
  colors?: readonly [string, string, ...string[]];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  style?: TextStyle;
  gradientStyle?: ViewStyle;
}

export default function GradientText({
  children,
  colors = ["#8C52FF", "#FF6B6B"] as const, // Default blue to purple gradient
  start = { x: 0.2, y: 0 },
  end = { x: 1, y: 0 },
  style,
  gradientStyle,
}: GradientTextProps) {
  const textStyle = [
    {
      fontFamily: fonts.poppinsSemibold,
      lineHeight: 28.8,
      fontSize: 24,
      fontWeight: "600" as const,
    },
    style,
  ];

  // Fallback for when MaskedView is not available or causing issues
  if (!MaskedView || Platform.OS === "web") {
    return (
      <LinearGradient
        colors={colors}
        start={start}
        end={end}
        style={[
          {
            borderRadius: 4,
            paddingHorizontal: 8,
            paddingVertical: 4,
          },
          gradientStyle,
        ]}
      >
        <Text
          style={[
            textStyle,
            {
              color: "white", // Fallback text color on gradient background
            },
          ]}
        >
          {children}
        </Text>
      </LinearGradient>
    );
  }

  // Original MaskedView implementation when available
  return (
    <MaskedView
      style={{ flexDirection: "row" }}
      maskElement={
        <Text
          style={[
            {
              backgroundColor: "transparent",
              ...textStyle[0],
            },
            style,
          ]}
        >
          {children}
        </Text>
      }
    >
      <LinearGradient
        colors={colors}
        start={start}
        end={end}
        style={[{ flex: 1 }, gradientStyle]}
      >
        <Text
          style={[
            textStyle,
            {
              opacity: 0,
            },
          ]}
        >
          {children}
        </Text>
      </LinearGradient>
    </MaskedView>
  );
}
