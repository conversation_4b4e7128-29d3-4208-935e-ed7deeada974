import React from "react";
import { Text, TextStyle, ViewStyle } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { fonts } from "../config/Fonts";

interface GradientTextSimpleProps {
  children: React.ReactNode;
  colors?: readonly [string, string, ...string[]];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  style?: TextStyle;
  gradientStyle?: ViewStyle;
}

/**
 * Simple gradient text component that doesn't rely on MaskedView
 * Uses a gradient background with white text as a fallback
 */
export default function GradientTextSimple({
  children,
  colors = ["#8C52FF", "#FF6B6B"] as const,
  start = { x: 0.2, y: 0 },
  end = { x: 1, y: 0 },
  style,
  gradientStyle,
}: GradientTextSimpleProps) {
  return (
    <LinearGradient
      colors={colors}
      start={start}
      end={end}
      style={[
        {
          borderRadius: 8,
          paddingHorizontal: 12,
          paddingVertical: 6,
          alignSelf: "flex-start",
        },
        gradientStyle,
      ]}
    >
      <Text
        style={[
          {
            color: "white",
            fontFamily: fonts.poppinsSemibold,
            fontSize: 24,
            fontWeight: "600",
            lineHeight: 28.8,
            textAlign: "center",
          },
          style,
        ]}
      >
        {children}
      </Text>
    </LinearGradient>
  );
}
