import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
} from "react-native";
import { fonts } from "../config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../config/Svg";
import P from "./P";
import { colorTone } from "react-native-color-matrix-image-filters";
import { colors } from "../config/colors";

const baseHeight = 802;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
interface PProps {
  onKeyPress: (key) => void;
  onEnter?: any;
  onBioMatric?: () => void;
  loading?: boolean;
}

const NewKeyboard = ({ onKeyPress, onEnter, onBioMatric, loading }: PProps) => {
  const keys = [
    ["1", "2", "3"],
    ["4", "5", "6"],
    ["7", "8", "9"],
    [".", "0", "←"],
  ];

  const handleKeyPress = (key) => {
    onKeyPress(key);
  };
  const [activeDot, setActiveDot] = useState(0);
  // Create an array of numbers [1, 2, 3] for the dots
  const dotsArray = [1, 2, 3];

  // Cycle through the dots every 500ms
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length); // Cycle through dots
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);
  return (
    <View style={styles.keyboardContainer}>
      {keys.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.keyRow}>
          {row.map((key) => (
            <TouchableOpacity
              key={key}
              style={styles.key}
              onPress={() => handleKeyPress(key)}
            >
              {key == "←" ? (
                <SvgXml xml={svg.backSpace} />
              ) : (
                <Text style={styles.keyText}>{key}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      ))}
      <View
        style={{
          width: "100%",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          marginTop: 18,
        }}
      >
        <TouchableOpacity style={styles.bioBtn} onPress={onBioMatric}>
          <SvgXml
            xml={Platform.OS === "ios" ? svg.faceIdGray : svg.fingerPrint}
            style={{ marginRight: 4 }}
          />
          <P style={{ color: colors.primary }}>
            {Platform.OS === "ios" ? "Face ID" : "Fingerprint"}
          </P>
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            width: "59%",
            height: (6.5 * height) / 100,
            backgroundColor: colors.primary,
            borderRadius: 18,
            alignItems: "center",
            justifyContent: "center",
          }}
          onPress={onEnter}
        >
          {loading ? (
            <View style={styles.loaderContainer}>
              {dotsArray.map((dot, index) => (
                <View
                  key={dot}
                  style={[
                    styles.dot,
                    {
                      backgroundColor:
                        activeDot === index ? colors.white : "#D0B8FF",
                    },
                  ]}
                />
              ))}
            </View>
          ) : (
            <>
              <P style={{ color: colors.white }}>Enter</P>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  keyboardContainer: {
    width: "100%",
  },
  keyRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 6,
  },
  key: {
    width: "32.5%",
    height: (6.5 * height) / 100,
    // height: (52 * height) / baseHeight,
    backgroundColor: "#fff",
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
  },
  keyText: {
    fontSize: 18,
    color: "#000",
    lineHeight: (27 / baseHeight) * height,
    fontFamily: fonts.poppinsMedium,
  },
  bioBtn: {
    flexDirection: "row",
    alignItems: "center",
    width: "40%",
    height: (6.5 * height) / 100,
    borderRadius: 18,
    borderWidth: 1,
    borderColor: colors.primary,
    justifyContent: "center",
    padding: 8,
    backgroundColor: colors.white,
  },
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30, // Space for the 3 dots
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 100,
    backgroundColor: colors.gray, // Default inactive color
    marginHorizontal: 2,
  },
});

export default NewKeyboard;
